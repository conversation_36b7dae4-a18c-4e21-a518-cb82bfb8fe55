
**Référence : [DEV - Ajout de XXX](https://docs.google.com/document/xxxxxx)**

## Pourquoi ?

<!-- Quelle partie de la fonctionnalité ce changement apporte-t-il ? Pourquoi est-ce que le projet avait besoin de ce refactoring ? etc… -->

## Quoi ?

<!-- Quels composants est-ce que ce changement introduit ou modifie ? Quel choix de techno, et pourquoi ? -->

## Comment ?

<!-- Quels choix et compromis avez-vous fait ? Inutile de détailler les lignes de codes, car c'est redondant avec le diff de code -->

## Tests

<!-- Comment est-ce que vous avez vérifié le bon fonctionnement du code ? Comment le relecteur peut le tester de son côté ? C'est ici qu'une vidéo ou capture d'écran peut s'avérer utile -->