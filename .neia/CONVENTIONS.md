# NEIA Python Project Conventions

This document describes the standard developer guidelines for Python projects managed by NEIA. These conventions are intended for both human developers and AI agents interacting with NEIA projects.

---

NEIA

## 1. Code Style

- **Follow [PEP 8](https://peps.python.org/pep-0008/)** for all Python code.
- Use 4 spaces per indentation level. Do not use tabs.
- Follow the rules of Black, isort, and MyPy that are configured in the `pyproject.toml` file.
- Use descriptive variable, function, and class names.
- Prettify the code with Black and isort.
- Avoid wildcard imports (`from module import *`).
- When inserting code, match the surrounding style and indentation.
- When in doubt, prefer explicitness and clarity over brevity.
- Refrain from adding comments in the code. Only add come comments in the tests if it's necessary, for example, to explain a complex logic.

## 2. Typing and Type Hints

- Use type hints for all function arguments and return values.
- Use `Optional`, etc., from the `typing` module as appropriate.
- Prefer `list`, `dict`, `tuple`, `set` over `List`, `Dict`, `Tuple`, `Set` from the `typing` module.
- Prefer explicit types over `Any` when possible.
- Avoid `# type: ignore`. Only use `# type: ignore` for extreme edge cases or when testing data for inside tests.

## 3. Common Patterns and Anti-patterns

- **Dependency Injection:** Use dependency injection to decouple components and improve testability.
- **Abstract Factories:** Use abstract factories to create families of related objects without specifying their concrete classes.
- **Singletons:** Avoid using singletons excessively. They can make code harder to test and reason about.
- **Global State:** Minimize the use of global state. It can make code harder to understand and debug.
- **Exception Handling:** Use exception handling to gracefully handle errors and prevent the application from crashing. Avoid catching generic exceptions (e.g., `except Exception:`). Catch specific exceptions and handle them appropriately.

## 4. Documentation

- Every public function, class, and method must have a docstring.
- Use triple double quotes (`"""Docstring"""`) for docstrings.
- Docstrings should describe the purpose, arguments, return values, and exceptions raised.
- Avoid documenting every step of the code, only document the most important or unintuitive parts.
- When generating or modifying code, always preserve existing docstrings and comments unless instructed otherwise.

## 5. Error Handling

- Use exceptions for error handling, not return codes.
- Catch only the exceptions you expect and can handle.
- Log errors using the project's logging utilities.

## 6. Logging

- Use the provided logging adapter (`get_logger`) for all logging.
- Do not use `print()` for logging or debugging.
- Use `click` for CLI display.

## 7. File and Directory Structure

- Place all source code in the main package directory (e.g., `neia/`).
- Tests should be placed in a separate `tests/` directory.
- Configuration files aimed at the AI (e.g., `.neia/CONVENTIONS.md`) should be placed in the `.neia/` directory.

## 8. Version Control

- All code must be committed to Git.
- Write clear, concise commit messages in English.
- Do not commit secrets or sensitive information.

## 9. Dependencies

- Dependencies are managed with Poetry.
- All dependencies are listed in `pyproject.toml`.
- Pin versions where possible to ensure reproducibility.
- Do not introduce new dependencies without updating the relevant dependency files.

## 10. Tooling

- Check Makefile for the list of commands to run the project.
- Always use the Makefile to run the tests.
- Always use the Makefile to run the linting.
- Always use the Makefile to run the formatting.
- Always use the Makefile to run the type checking.
- Always use the Makefile to run the code coverage.

---

_These conventions help ensure code quality, maintainability, and consistency across all NEIA Python projects. Please follow them strictly, whether you are a human developer or an AI agent._
