import logging
import os
from typing import Optional

import click
from dotenv import load_dotenv

from neia.code_reviewer import <PERSON><PERSON><PERSON>iewer
from neia.gitlab_client import GitLabClient
from neia.google_client import generate_token
from neia.issue_loader import get_issue_from_url
from neia.repository_analyzer import analyze

load_dotenv(override=True)

gitlab_url = os.getenv("GITLAB_URL")
gitlab_token = os.getenv("GITLAB_TOKEN")
gitlab_project_id = os.getenv("GITLAB_PROJECT_ID")
tuleap_api_url = os.getenv("TULEAP_API_URL")
tuleap_user = os.getenv("TULEAP_USER")
tuleap_password = os.getenv("TULEAP_PASSWORD")
google_token_file = os.getenv("GOOGLE_TOKEN_FILE", "token.json")

if not gitlab_url or not gitlab_token or not gitlab_project_id:
    raise ValueError(
        "GITLAB_URL, GITLAB_TOKEN and GITLAB_PROJECT_ID environment variables are required"
    )

gitlab_client = GitLabClient(gitlab_url, gitlab_token, gitlab_project_id)


@click.group()
@click.option(
    "--debug",
    is_flag=True,
    help="Show debug messages",
)
def cli(debug: bool) -> None:
    if debug:
        logging.basicConfig(level=logging.DEBUG)


@cli.command()
@click.argument("issue_url")
@click.option("--dry-run", is_flag=True, help="Show changes without creating MR")
@click.option(
    "--prompt-only",
    is_flag=True,
    help="Show the prompt that would be sent to LLM without running it",
)
@click.option(
    "--bypass-action-plan",
    is_flag=True,
    help="Bypass the displaying and validating of an action plan and execute directly",
)
def process_issue(
    issue_url: str, dry_run: bool, prompt_only: bool, bypass_action_plan: bool
) -> None:
    """Process GitLab issues and apply changes."""
    if not gitlab_project_id:
        click.echo("Error: GITLAB_PROJECT_ID environment variable is required")
        return

    try:
        should_assign_issue = not (dry_run or prompt_only)
        issue = get_issue_from_url(issue_url, gitlab_project_id, should_assign_issue)
    except Exception as e:
        click.echo(e)
        return

    branch_name = f"neia/{issue.source}-{issue.id}"

    # Get MR if it exists already
    mr = gitlab_client.get_merge_request_by_branch(gitlab_project_id, branch_name)

    analyze(
        gitlab_client,
        gitlab_project_id,
        branch_name,
        issue,
        mr,
        dry_run,
        prompt_only,
        bypass_action_plan,
    )


@cli.command()
@click.option("--export", type=click.Choice(["json", "csv"]), help="Export format")
def list_mrs(export: Optional[str] = None) -> None:
    """List all merge requests."""
    gitlab_client.print_merge_requests_stats(export)


@cli.command()
@click.argument("mr_id")
def show_mr(mr_id: str) -> None:
    """Show details of a merge request."""
    gitlab_client.print_merge_request_details(mr_id)


@cli.command()
@click.argument("mr_iid")
@click.option("--dry-run", is_flag=True, help="Show changes without creating MR")
@click.option(
    "--prompt-only",
    is_flag=True,
    help="Show the prompt that would be sent to LLM without running it",
)
def process_mr(mr_iid: str, dry_run: bool, prompt_only: bool):
    """Process a merge request."""
    gitlab_client.process_merge_request(dry_run, prompt_only, mr_iid=mr_iid)


@cli.command()
@click.argument("mr_iid")
@click.option("--dry-run", is_flag=True, help="Show changes without creating MR")
def review_mr(mr_iid: str, dry_run: bool = False) -> None:
    """Perform automated code review on merge requests."""

    if not gitlab_project_id:
        click.echo("Error: GITLAB_PROJECT_ID environment variable is required")
        return
    llm_model = os.getenv("LLM_MODEL", "gpt-4o-mini")
    code_reviewer = CodeReviewer(gitlab_client, gitlab_project_id, llm_model)
    code_reviewer.review(mr_iid=mr_iid, dry_run=dry_run)


@cli.command()
@click.option(
    "--credentials-file",
    default="credentials.json",
    help="Google credentials file used to generate token",
)
@click.option(
    "--token-file", default="token.json", help="Google token file to be saved"
)
def generate_google_token(credentials_file: str, token_file: str) -> None:
    """Generate Google token."""
    generate_token(credentials_file, token_file)


def run() -> None:
    cli()


if __name__ == "__main__":
    cli()
