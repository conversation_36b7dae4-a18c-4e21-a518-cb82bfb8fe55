from __future__ import annotations

from enum import Enum
from pathlib import Path

import git
from aider.coders import Coder
from aider.io import InputOutput
from aider.models import Model
from aider.repo import GitRepo
from dotenv import dotenv_values

from neia.local_repository_manager import LocalRepositoryManager
from neia.logging.neia_logging_adapter import get_logger

logger = get_logger("AiderManager")


class OutputFormat(Enum):
    """Available output formats for coder responses."""

    TEXT = "text"
    JSON = "json"


class AiderCoderConfig:
    """Configuration for creating aider coders."""

    def __init__(
        self,
        model_name: str = "gpt-4o-mini",
        edit_format: str | None = None,
        detect_urls: bool = False,
        attribute_author: bool = False,
        attribute_committer: bool = False,
        yes_always: bool = True,
        conventions_file_path: str | None = None,
    ):
        self.model_name = model_name
        self.edit_format = edit_format
        self.detect_urls = detect_urls
        self.attribute_author = attribute_author
        self.attribute_committer = attribute_committer
        self.yes_always = yes_always
        self.conventions_file_path = conventions_file_path


class ManagedCoder:
    """Wrapper around aider Coder that provides additional functionality."""

    def __init__(self, coder: Coder):
        self._coder = coder
        self._prompts: list[str] = []
        self._technology_levels_enabled: bool = False
        self._technology_levels_prompt: str = ""

    def add_prompt(self, prompt: str) -> None:
        """Add a prompt to the queue."""
        self._prompts.append(prompt)
        logger.debug(f"Added prompt to queue. Total prompts: {len(self._prompts)}")

    def run(self) -> str:
        """Execute all queued prompts and return the result."""
        if not self._prompts:
            logger.warning("No prompts to execute")
            return ""

        if len(self._prompts) == 1:
            response = self._execute_single_prompt(self._prompts[0])
        else:
            combined_prompt = "\n\n".join(self._prompts)
            response = self._execute_single_prompt(combined_prompt)

        self._prompts.clear()
        return response

    def run_single(self, prompt: str) -> str:
        """Execute a single prompt immediately without queuing."""
        return self._execute_single_prompt(prompt)

    def _execute_single_prompt(self, prompt: str) -> str:
        """Execute a single prompt using the underlying coder."""
        try:
            enhanced_prompt = self._enhance_prompt_with_technology_level(prompt)
            logger.debug(f"Executing prompt with {len(enhanced_prompt)} characters")
            return self._coder.run(enhanced_prompt)
        except Exception as e:
            logger.error(f"Error executing prompt: {e}")
            raise

    def _enhance_prompt_with_technology_level(self, prompt: str) -> str:
        """Enhance the prompt with technology level context if enabled."""
        if self._technology_levels_enabled and self._technology_levels_prompt:
            return f"{self._technology_levels_prompt}\n\n{prompt}"
        return prompt

    @property
    def pending_prompts_count(self) -> int:
        """Get the number of pending prompts in the queue."""
        return len(self._prompts)

    @property
    def technology_level_enabled(self) -> bool:
        """Check if technology level adaptation is enabled."""
        return self._technology_levels_enabled

    def clear_prompts(self) -> None:
        """Clear all pending prompts."""
        self._prompts.clear()
        logger.debug("Cleared all pending prompts")

    def enable_technology_level(self) -> None:
        """Enable technology level adaptation based on environment variables."""
        technology_levels = {
            key.replace("TECHNOLOGY_LEVEL_", ""): int(value)
            for key, value in dotenv_values().items()
            if key.startswith("TECHNOLOGY_LEVEL_")
            and value is not None
            and value.isdigit()
            and 1 <= int(value) <= 5
        }

        if technology_levels:
            print("\n\nDeclared technology levels:")
            for technology, level in technology_levels.items():
                print(f"{technology}: {level}/5")
            self._technology_levels_prompt = (
                "The user declares having the following technology levels (1 is beginner, 5 is expert): "
                + str(technology_levels)
                + ". Adapt all your answers to their level, explain everything more in detail and in a simpler way if the declared level is lower."
            )
            self._technology_levels_enabled = True
            logger.debug("Technology level adaptation enabled")
        else:
            print("\n\nNo declared technology levels.")
            print(
                "You can declare your technology levels (`TECHNOLOGY_LEVEL_*`) in the `.env` file, more info in `README.md`.\n"
            )
            self._technology_levels_enabled = False


class AiderManager:
    """Factory and manager for aider coders."""

    def __init__(self, repo_manager: LocalRepositoryManager):
        self.repo_manager = repo_manager

    @classmethod
    def create_coder(
        cls,
        repo_path: str,
        config: AiderCoderConfig | None = None,
        project_name: str | None = None,
    ) -> ManagedCoder:
        """Create a managed coder instance with the specified configuration."""
        if config is None:
            config = AiderCoderConfig()

        logger.debug(f"Creating coder for repo: {repo_path}")

        model = Model(config.model_name)
        io = InputOutput(yes=config.yes_always, root=repo_path)

        fnames = cls._get_convention_files(repo_path, config, project_name)

        git_repo = GitRepo(
            io,
            [repo_path],
            None,
            models=model.commit_message_models(),
            attribute_author=config.attribute_author,
            attribute_committer=config.attribute_committer,
        )

        coder = Coder.create(
            io=io,
            edit_format=config.edit_format,
            main_model=model,
            fnames=fnames,
            repo=git_repo,
            detect_urls=config.detect_urls,
        )

        return ManagedCoder(coder)

    @classmethod
    def create_ask_coder(
        cls,
        repo_path: str,
        config: AiderCoderConfig | None = None,
        project_name: str | None = None,
    ) -> ManagedCoder:
        """Create a managed coder in 'ask' mode for analysis without editing."""
        if config is None:
            config = AiderCoderConfig()

        ask_config = AiderCoderConfig(
            model_name=config.model_name,
            edit_format="ask",
            detect_urls=config.detect_urls,
            attribute_author=config.attribute_author,
            attribute_committer=config.attribute_committer,
            yes_always=config.yes_always,
            conventions_file_path=config.conventions_file_path,
        )

        return cls.create_coder(repo_path, ask_config, project_name)

    @staticmethod
    def _get_convention_files(
        repo_path: str,
        config: AiderCoderConfig,
        project_name: str | None = None,
    ) -> list[str]:
        """Get list of convention files to include in the coder context."""
        fnames = []

        if config.conventions_file_path:
            convention_file = Path(config.conventions_file_path)
            if convention_file.exists():
                fnames.append(str(convention_file))
        else:
            convention_file = Path(repo_path) / ".neia/CONVENTIONS.md"
            if convention_file.exists():
                fnames.append(str(convention_file))

        logger.debug(f"Using convention files: {fnames}")
        return fnames

    def create_coder_for_git_repo(
        self,
        repo: git.Repo,
        config: AiderCoderConfig | None = None,
    ) -> ManagedCoder:
        """Create a managed coder for an existing git repository."""
        if not repo.working_dir:
            raise ValueError("Repository must have a working directory")

        return self.create_coder(str(repo.working_dir), config)

    def create_ask_coder_for_git_repo(
        self,
        repo: git.Repo,
        config: AiderCoderConfig | None = None,
    ) -> ManagedCoder:
        """Create a managed ask coder for an existing git repository."""
        if not repo.working_dir:
            raise ValueError("Repository must have a working directory")

        return self.create_ask_coder(str(repo.working_dir), config)
