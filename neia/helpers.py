from typing import Optional, Union

from gitlab.base import RESTObject

from neia.gitlab_client import ProjectMergeRequest

NEIA_COMMENT_AUTHOR = "NEIA"


def try_parse_int(value: Union[str, int]) -> Optional[int]:
    try:
        return int(value)
    except ValueError:
        return None


def resume_mr_discussion(
    mr: ProjectMergeRequest, discussion: RESTObject
) -> Optional[str]:
    comments: list[str] = []
    last_note_reactions: list[RESTObject] = []
    notes = discussion.attributes["notes"]
    # Loop on all comments in the discussion
    for note_data in notes:
        note = mr.notes.get(note_data["id"])
        # System and resolved comments are ignored
        if note.system or (note.resolvable and note.resolved):
            continue
        last_note_reactions = list(note.awardemojis.list())
        body = note.body.strip()
        # Check if body contains "[NEIA]"
        author = NEIA_COMMENT_AUTHOR if "[NEIA]" in body else note.author["name"]
        if note.type == "DiffNote" and note.position:
            # Comments on the code
            pos = note.position
            commit_id = note.commit_id[:7] if note.commit_id else "N/A"
            file_path = pos.get("new_path") or pos.get("old_path") or "<unknown file>"

            # Gestion des lignes avec line_range pour les commentaires multi-lignes
            if "line_range" in pos:
                line_range = pos["line_range"]
                start = line_range["start"]
                end = line_range["end"]
                start_line = start.get("new_line") or start.get("old_line")
                end_line = end.get("new_line") or end.get("old_line")
                if start_line == end_line:
                    line = f"{start_line}"
                    line_label = "Line"
                else:
                    line = f"{start_line}-{end_line}"
                    line_label = "Lines"
            else:
                # Fallback pour les commentaires sur une seule ligne
                line = pos.get("new_line") or pos.get("old_line") or "?"
                line_label = "Line"

            comment = f"{author} (Commit: {commit_id}, File: {file_path}, {line_label}: {line}): {body}"
        else:
            # Normal comments
            comment = f"{author}: {body}"
        comments.append(comment)
    # The thread is ignored if there are no usable comments, if the last comment is from NEIA, or if the last comment is marked as checked
    if (
        not comments
        or comments[-1].startswith(NEIA_COMMENT_AUTHOR)
        or any(reaction.name == "white_check_mark" for reaction in last_note_reactions)
    ):
        return None
    return "\n".join(comments)
