import os
from typing import Optional

from google.auth.exceptions import Refresh<PERSON><PERSON>r
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

from neia.google_docs_deserializer import (
    ContentElement,
    GoogleDocsDeserializer,
    GoogleDocumentData,
)
from neia.models import NeiaIssue

# If modifying these scopes, delete the file token.json.
SCOPES = ["https://www.googleapis.com/auth/documents.readonly"]


class GoogleClient:
    token_file: str
    credentials: Optional[Credentials] = None

    def __init__(self, token_file: str):
        self.token_file = token_file

    @staticmethod
    def get_token_file_path(token_file: str) -> str:
        config_dir = os.environ.get("NEIA_CONFIG_DIR")
        if config_dir:
            return os.path.join(config_dir, token_file)
        return token_file

    def auth(self) -> bool:
        token_file_path = GoogleClient.get_token_file_path(self.token_file)
        if os.path.exists(token_file_path):
            creds = Credentials.from_authorized_user_file(token_file_path, SCOPES)
            if creds and creds.valid:
                self.credentials = creds
                return True
        return False

    def parse_content(self, content: list[ContentElement]) -> str:
        """Parse structured content elements into a text string"""
        result_parts = []
        for element in content:
            text_content = element.get_text_content()
            if text_content:
                result_parts.append(text_content)

        return " ".join(result_parts) if result_parts else ""

    def get_issue(self, document_id: str) -> NeiaIssue:
        service = build("docs", "v1", credentials=self.credentials)
        document: GoogleDocumentData = (
            service.documents().get(documentId=document_id).execute()
        )

        # Extract and deserialize content
        body = document.get("body", {})
        raw_content = body.get("content", [])
        structured_content = GoogleDocsDeserializer.deserialize_content(raw_content)

        # Parse content to text
        description = self.parse_content(structured_content)

        return NeiaIssue(
            id=document.get("documentId", ""),
            source="gdocs",
            title=document.get("title", ""),
            description=description,
            comments=[],
        )


def generate_token(credentials_file: str, token_file: str) -> None:
    # The token file stores the user's access and refresh tokens, and is
    # created automatically when the authorization flow completes for the first
    # time.
    # Prefix token_file with NEIA_CONFIG_DIR if the environment variable is set
    token_file_path = GoogleClient.get_token_file_path(token_file)

    creds = None
    if os.path.exists(token_file_path):
        creds = Credentials.from_authorized_user_file(token_file_path, SCOPES)
    if creds and creds.valid:
        print("Valid token already exists.")
        return
    try:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            raise RefreshError("No valid creds to refresh, need login")
    except RefreshError:
        # If there are no valid credentials available or the refresh is not successful, let the user log in.
        flow = InstalledAppFlow.from_client_secrets_file(
            credentials_file, SCOPES, redirect_uri="urn:ietf:wg:oauth:2.0:oob"
        )
        auth_url, _ = flow.authorization_url(prompt="consent")
        print(f"Please go to this URL: {auth_url}")

        code = input("Enter the authorization code: ")
        flow.fetch_token(code=code)
        creds = flow.credentials
    # Save the credentials for the next run
    with open(token_file_path, "w") as token:
        token.write(creds.to_json())
    print("The token was generated.")
