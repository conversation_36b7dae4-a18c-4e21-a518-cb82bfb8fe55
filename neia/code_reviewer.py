from datetime import datetime
from typing import Any, Optional

import click
from gitlab.v4.objects import ProjectMergeRequest

from neia.aider_manager import <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Aider<PERSON>ana<PERSON>, ManagedCoder
from neia.gitlab_client import GitLabClient
from neia.local_repository_manager import LocalRepositoryManager
from neia.logging.neia_logging_adapter import get_logger
from neia.models import (
    QualityMetric,
    ReviewComment,
    ReviewCommentSeverity,
    ReviewCommentType,
    ReviewResults,
)

logger = get_logger("CodeReviewer")


class CodeReviewer:
    """Automated code reviewer for GitLab merge requests."""

    def __init__(
        self,
        gitlab_client: GitLabClient,
        project_id: str,
        model_name: str = "gpt-4o-mini",
        base_path: str = "projects",
    ):
        self.gitlab_client = gitlab_client
        self.project_id = project_id
        self.model_name = model_name
        self.repo_manager = LocalRepositoryManager(base_path)
        self.project = self.gitlab_client.get_project(project_id)

    def review(
        self,
        *,
        mr_id: Optional[str] = None,
        mr_iid: Optional[str] = None,
        dry_run: bool = False,
    ) -> Optional[ReviewResults]:
        """
        Review a merge request for quality with user feedback.
        """
        if not mr_id and not mr_iid:
            click.echo("Error: Either mr_id or mr_iid is required")
            logger.error("review: MR ID or IID is required")
            return None

        mr_identifier = mr_id or mr_iid
        click.echo(f"🔍 Analyzing Merge Request #{mr_identifier}...")

        try:
            review_result = self.analyze_merge_request(mr_id=mr_id, mr_iid=mr_iid)

            if not review_result:
                click.echo(f"❌ Failed to analyze MR #{mr_identifier}")
                return None

            click.echo(f"\n📊 Review Results for MR #{review_result.mr_iid}:")
            click.echo(f"Overall Score: {review_result.overall_score:.1f}/10")
            click.echo(f"Issues Found: {len(review_result.inline_comments or [])}")
            click.echo(
                f"DoD Issues: {len(review_result.definition_of_done_issues or [])}"
            )

            if dry_run:
                click.echo("\n🔍 DRY RUN - Review Summary:")
                click.echo("=" * 50)
                click.echo(review_result.summary or "No summary available")

                if review_result.inline_comments:
                    click.echo(
                        f"\n📝 Inline Comments ({len(review_result.inline_comments)}):"
                    )
                    for i, comment in enumerate(review_result.inline_comments, 1):
                        click.echo(f"\n{i}. {comment.file_path}:{comment.line_number}")
                        click.echo(
                            f"   [{comment.comment_type.value}] {comment.message}"
                        )
                        if comment.suggestion:
                            click.echo(f"   Suggestion: {comment.suggestion}")

                if review_result.quality_metrics:
                    click.echo("\n📊 Quality Metrics:")
                    for metric in review_result.quality_metrics:
                        click.echo(
                            f"   {metric.name}: {metric.score:.1f}/10 - {metric.description}"
                        )

                if review_result.recommendations:
                    click.echo("\n💡 Recommendations:")
                    for rec in review_result.recommendations:
                        click.echo(f"   - {rec}")
            else:
                success = self.post_review_to_gitlab(review_result)
                if not success:
                    click.echo("❌ Failed to post review to GitLab")
                    return review_result

                click.echo("✅ Review posted successfully!")
                mr = self.gitlab_client.get_merge_request(mr_id=mr_id, mr_iid=mr_iid)
                if mr:
                    click.echo(f"📎 View at: {mr.web_url}")

            return review_result

        except Exception as e:
            click.echo(f"❌ Error reviewing MR #{mr_identifier}: {e}")
            logger.error(f"Review error for MR {mr_identifier}: {e}")
            return None

    def analyze_merge_request(
        self,
        *,
        mr_id: Optional[str] = None,
        mr_iid: Optional[str] = None,
    ) -> Optional[ReviewResults]:
        """
        Analyze a merge request and generate automated review.
        """
        if not mr_id and not mr_iid:
            logger.error("analyze_merge_request: MR ID or IID is required")
            return None

        try:
            mr = self.gitlab_client.get_merge_request(mr_id=mr_id, mr_iid=mr_iid)
            if not mr:
                logger.error(f"Merge request {mr_id or mr_iid} not found")
                return None

            if mr.state != "opened":
                logger.error(f"Merge request {mr_id or mr_iid} is not in opened state")
                return None

            repo = self.repo_manager.ensure_local_repository(
                self.project.name, self.project.git_url
            )

            self.repo_manager.ensure_branch(repo, mr.source_branch)

            repo_path = str(repo.working_dir)

            changes = self._get_mr_changes(mr)
            if not changes:
                logger.warning(f"No changes found in MR {mr_id or mr_iid}")
                return None

            if self._is_mr_too_large(changes):
                logger.warning(
                    f"MR {mr_id or mr_iid} is too large for automated review"
                )
                return self._create_large_mr_review(mr)

            return self._generate_review(mr, changes, repo_path)

        except Exception as e:
            logger.error(f"Error analyzing merge request {mr_id or mr_iid}: {e}")
            return None

    def _get_mr_changes(self, mr: ProjectMergeRequest) -> Optional[dict[str, Any]]:
        """Get the file changes from a merge request."""
        try:
            changes = mr.changes()
            if hasattr(changes, "json"):
                return changes.json()
            elif isinstance(changes, dict):
                return changes
            else:
                return dict(changes) if changes else None
        except Exception as e:
            logger.error(f"Error getting MR changes: {e}")
            return None

    def _is_mr_too_large(self, changes: dict[str, Any]) -> bool:
        """Check if MR is too large for efficient analysis."""
        if not changes or "changes" not in changes:
            return False

        file_count = len(changes["changes"])
        total_lines_changed = 0

        for change in changes["changes"]:
            if change.get("diff"):
                diff_lines = len(change["diff"].split("\n"))
                total_lines_changed += diff_lines

        return file_count > 50 or total_lines_changed > 5000

    def _create_large_mr_review(self, mr: ProjectMergeRequest) -> ReviewResults:
        """Create a review result for MRs that are too large to analyze in detail."""
        return ReviewResults(
            mr_id=str(mr.id),
            mr_iid=str(mr.iid),
            overall_score=5.0,
            summary="Cette MR est trop volumineuse pour une analyse automatique détaillée. "
            "Une revue humaine complète est recommandée.",
            inline_comments=[],
            quality_metrics=[
                QualityMetric(
                    name="Taille de la MR",
                    score=3.0,
                    description="MR trop volumineuse pour analyse automatique",
                    issues=["Plus de 50 fichiers ou 5000 lignes modifiées"],
                )
            ],
            definition_of_done_issues=[
                "MR trop volumineuse - vérification manuelle requise pour la Definition of Done"
            ],
            recommendations=[
                "Considérer diviser cette MR en plusieurs plus petites",
                "Effectuer une revue humaine approfondie",
                "Vérifier manuellement tous les critères de qualité",
            ],
            analysis_timestamp=datetime.now().isoformat(),
        )

    def _generate_review(
        self,
        mr: ProjectMergeRequest,
        changes: dict[str, Any],
        repo_path: str,
    ) -> ReviewResults:
        """Generate comprehensive code review using AI analysis."""

        context = self._prepare_analysis_context(mr, changes)

        quality_metrics = self._analyze_code_quality(context, repo_path)
        inline_comments = self._generate_inline_comments(context, repo_path)
        dod_issues = self._check_definition_of_done(context, repo_path)
        overall_score, summary = self._generate_overall_assessment(
            quality_metrics, inline_comments, dod_issues
        )
        recommendations = self._generate_recommendations(
            quality_metrics, inline_comments, dod_issues
        )

        return ReviewResults(
            mr_id=str(mr.id),
            mr_iid=str(mr.iid),
            overall_score=overall_score,
            summary=summary,
            inline_comments=inline_comments,
            quality_metrics=quality_metrics,
            definition_of_done_issues=dod_issues,
            recommendations=recommendations,
            analysis_timestamp=datetime.now().isoformat(),
        )

    def _prepare_analysis_context(
        self, mr: ProjectMergeRequest, changes: dict[str, Any]
    ) -> str:
        """Prepare context string for AI analysis."""
        context_parts = [
            "Merge Request Analysis",
            f"Title: {mr.title}",
            f"Description: {mr.description or 'No description'}",
            f"Source Branch: {mr.source_branch}",
            f"Target Branch: {mr.target_branch}",
            "",
            "Changes Summary:",
        ]

        if "changes" in changes:
            for change in changes["changes"]:
                file_path = change.get("new_path", change.get("old_path", "unknown"))
                context_parts.append(f"- {file_path}")

                if change.get("diff"):
                    diff = change["diff"]
                    if len(diff) > 2000:
                        diff = diff[:2000] + "\n... (truncated)"
                    context_parts.append(f"  Diff:\n{diff}")

        return "\n".join(context_parts)

    def _create_coder(self, repo_path: str) -> ManagedCoder:
        """Create and configure an Aider coder instance for AI analysis."""
        convention_file = self.repo_manager.get_conventions_file_path(self.project.name)

        config = AiderCoderConfig(
            model_name=self.model_name,
            edit_format="ask",
            detect_urls=False,
            attribute_author=False,
            attribute_committer=False,
            yes_always=True,
            conventions_file_path=(
                str(convention_file) if convention_file.exists() else None
            ),
        )

        coder = AiderManager.create_coder(repo_path, config, self.project.name)
        coder.enable_technology_level()
        return coder

    def _analyze_code_quality(
        self, context: str, repo_path: str
    ) -> list[QualityMetric]:
        """Analyze code quality using AI."""
        prompt = f"""Analyse la merge request suivante pour détecter des problèmes de qualité du code.

IMPORTANT: Concentre-toi UNIQUEMENT sur le code qui a été modifié dans cette MR (lignes commençant par + ou - dans les diffs).
N'analyse PAS le code existant non modifié. Ne commente que les changements apportés.

Concentre-toi sur les changements concernant :
- La structure et l'organisation du nouveau code ajouté
- Les implications sur la performance des modifications
- Les considérations de sécurité des changements
- Le respect des bonnes pratiques dans le code modifié
- Les aspects de maintenabilité des modifications

Contexte (analyser uniquement les lignes de diff + et -) :
{context}

Retourne un objet JSON exactement dans ce format, sans markdown ni autre texte :
{{"metrics": [
  {{
    "name": "Structure du code",
    "score": 8.5,
    "description": "Bien organisé avec une séparation claire des responsabilités",
    "issues": ["Mineur : certaines fonctions pourraient être plus courtes"]
  }}
]}}
Tous les textes doivent être en français.
"""

        try:
            coder = self._create_coder(repo_path)

            import signal

            def timeout_handler(signum, frame):
                raise TimeoutError("AI analysis timed out")

            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(120)  # 2 minutes timeout

            try:
                response = coder.run_single(prompt)
            finally:
                signal.alarm(0)

            try:
                import json

                data = json.loads(response)
                metrics = []
                for metric_data in data.get("metrics", []):
                    metrics.append(
                        QualityMetric(
                            name=metric_data["name"],
                            score=float(metric_data["score"]),
                            description=metric_data["description"],
                            issues=metric_data.get("issues", []),
                        )
                    )
                return metrics
            except (json.JSONDecodeError, KeyError, ValueError):
                return self._create_fallback_metrics("AI response parsing failed")

        except Exception as e:
            logger.error(f"Error in code quality analysis: {e}")
            return self._create_fallback_metrics(f"Analysis error: {str(e)}")

    def _generate_inline_comments(
        self, context: str, repo_path: str
    ) -> list[ReviewComment]:
        """Generate inline comments for specific issues found in the code."""
        prompt = f"""
Analyser les changements de cette merge request et identifier les problèmes spécifiques qui méritent des commentaires en ligne.

RÈGLE CRITIQUE: Générer des commentaires UNIQUEMENT pour les lignes de code qui ont été modifiées dans cette MR.
- Regarder seulement les lignes qui commencent par "+" (ajoutées) ou "-" (supprimées) dans les diffs
- NE PAS commenter le code existant non modifié
- NE PAS commenter les lignes de contexte (commençant par un espace)
- Les numéros de ligne doivent correspondre aux nouvelles lignes ajoutées (lignes +)

Suivre le format Conventional Comments (https://conventionalcomments.org/):
- Utiliser des labels appropriés: nitpick, suggestion, issue, question, thought
- Être constructif et spécifique
- Fournir des commentaires exploitables quand possible

Rechercher dans les lignes modifiées SEULEMENT:
- Code smells (utiliser 'issue' ou 'suggestion')
- Bugs potentiels (utiliser 'issue')
- Problèmes de performance (utiliser 'issue' ou 'suggestion')
- Vulnérabilités de sécurité (utiliser 'issue')
- Violations de style (utiliser 'nitpick')
- Erreurs de logique (utiliser 'issue')
- Questions sur du code peu clair (utiliser 'question')

Contexte (commenter uniquement les lignes + et - des diffs):
{context}

Retourner un objet JSON dans ce format exact, sans markdown ou autre texte:
{{
  "comments": [
    {{
      "file_path": "src/example.py",
      "line_number": 42,
      "comment_type": "issue",
      "message": "Cette fonction est trop complexe et devrait être divisée",
      "suggestion": "Considérer extraire la logique de validation dans une fonction séparée",
      "severity": "major"
    }},
    {{
      "file_path": "src/utils.py",
      "line_number": 88,
      "comment_type": "nitpick",
      "message": "Considérer utiliser un nom de variable plus descriptif",
      "suggestion": "Remplacer 'x' par 'user_count' pour une meilleure lisibilité",
      "severity": "minor"
    }}
  ]
}}

Valeurs valides pour severity: minor, major, critical
Tous les textes doivent être en français.
"""

        try:
            coder = self._create_coder(repo_path)

            response = coder.run_single(prompt)

            try:
                import json

                data = json.loads(response)
                comments = []
                for comment_data in data.get("comments", []):
                    try:
                        comment_type = ReviewCommentType(comment_data["comment_type"])
                    except ValueError:
                        comment_type = ReviewCommentType.ISSUE

                    try:
                        severity = ReviewCommentSeverity(comment_data["severity"])
                    except ValueError:
                        severity = ReviewCommentSeverity.MINOR

                    comments.append(
                        ReviewComment(
                            file_path=comment_data["file_path"],
                            line_number=int(comment_data["line_number"]),
                            comment_type=comment_type,
                            message=comment_data["message"],
                            suggestion=comment_data.get("suggestion"),
                            severity=severity,
                        )
                    )
                return comments
            except (json.JSONDecodeError, KeyError, ValueError):
                logger.warning("Failed to parse inline comments response")
                return []

        except Exception as e:
            logger.error(f"Error generating inline comments: {e}")
            return []

    def _check_definition_of_done(self, context: str, repo_path: str) -> list[str]:
        """Check if the merge request meets Definition of Done criteria."""
        prompt = f"""
Examinez cette demande de fusion selon les critères courants de la Definition of Done, en vous concentrant UNIQUEMENT sur les modifications apportées :

IMPORTANT: Évaluez seulement les changements effectués dans cette MR (lignes + et - dans les diffs), pas l'ensemble du codebase.

Critères à vérifier pour les modifications :
- Le nouveau code ajouté est correctement testé (tests unitaires, tests d'intégration)
- Les modifications respectent les standards de codage de l'équipe
- La documentation est mise à jour pour refléter les changements si nécessaire
- Aucune vulnérabilité de sécurité introduite par les modifications
- L'impact sur la performance des changements est acceptable
- Les modifications ne cassent pas le pipeline CI/CD

Contexte (évaluer uniquement les changements dans les diffs) :
{context}

Listez tout élément de la Definition of Done qui semble manquer ou être incomplet POUR LES MODIFICATIONS APPORTÉES.
Répondez avec une liste simple de problèmes, un par ligne.
Si tout semble correct pour les changements, répondez par « Aucun problème de Definition of Done trouvé. »
Tous les textes doivent être en français.
"""

        try:
            coder = self._create_coder(repo_path)

            response = coder.run_single(prompt)

            if "No Definition of Done issues found" in response:
                return []

            issues = [line.strip() for line in response.split("\n") if line.strip()]
            return issues

        except Exception as e:
            logger.error(f"Error checking Definition of Done: {e}")
            return [f"Unable to check Definition of Done: {str(e)}"]

    def _generate_overall_assessment(
        self,
        quality_metrics: list[QualityMetric],
        inline_comments: list[ReviewComment],
        dod_issues: list[str],
    ) -> tuple[float, str]:
        """Generate overall score and summary."""
        if quality_metrics:
            avg_score = sum(metric.score for metric in quality_metrics) / len(
                quality_metrics
            )
        else:
            avg_score = 5.0

        critical_issues = len([c for c in inline_comments if c.severity == "critical"])
        major_issues = len([c for c in inline_comments if c.severity == "major"])

        score_penalty = critical_issues * 2.0 + major_issues * 0.5
        final_score = max(0, avg_score - score_penalty)

        if dod_issues:
            final_score = max(0, final_score - len(dod_issues) * 0.3)

        summary_parts = [
            f"Score de qualité globale: {final_score:.1f}/10",
            "",
            f"Trouvé {len(inline_comments)} problèmes de code:",
            f"- Critique: {critical_issues}",
            f"- Important: {major_issues}",
            f"- Mineur: {len([c for c in inline_comments if c.severity == 'minor'])}",
            "",
        ]

        if quality_metrics:
            summary_parts.append("Métriques de qualité:")
            for metric in quality_metrics:
                summary_parts.append(f"- {metric.name}: {metric.score:.1f}/10")
            summary_parts.append("")

        if dod_issues:
            summary_parts.append(f"Problèmes de Definition of Done: {len(dod_issues)}")
        else:
            summary_parts.append(
                "✅ Tous les critères de Definition of Done sont respectés"
            )

        return final_score, "\n".join(summary_parts)

    def _generate_recommendations(
        self,
        quality_metrics: list[QualityMetric],
        inline_comments: list[ReviewComment],
        dod_issues: list[str],
    ) -> list[str]:
        """Generate actionable recommendations."""
        recommendations = []

        for metric in quality_metrics:
            if metric.score < 7.0:
                recommendations.append(
                    f"Improve {metric.name.lower()}: {metric.description}"
                )

        critical_issues = [c for c in inline_comments if c.severity == "critical"]
        if critical_issues:
            recommendations.append(
                f"Address {len(critical_issues)} critical security/stability issues before merging"
            )

        if dod_issues:
            recommendations.append(
                "Complete Definition of Done requirements before merging"
            )

        if not recommendations:
            recommendations.append(
                "Code looks good! Consider the inline suggestions for further improvements."
            )

        return recommendations

    def _create_fallback_metrics(
        self, reason: str = "Automated analysis failed"
    ) -> list[QualityMetric]:
        """Create fallback quality metrics when AI analysis fails."""
        return [
            QualityMetric(
                name="Code Quality",
                score=5.0,
                description=f"Could not analyze - {reason}. Manual review recommended.",
                issues=[reason],
            )
        ]

    def _extract_line_codes_from_mr(self, mr_obj) -> dict[str, dict[int, str]]:
        """Extract real line codes from GitLab MR diff data."""
        line_codes: dict[str, dict[int, str]] = {}
        try:
            changes = mr_obj.changes()

            if hasattr(changes, "changes") and changes.changes:
                for file_change in changes.changes:
                    file_path = file_change.get("new_path") or file_change.get(
                        "old_path"
                    )
                    if not file_path:
                        continue

                    line_codes[file_path] = {}

                    diff_text = file_change.get("diff", "")
                    if diff_text:
                        self._parse_diff_for_real_line_codes(
                            diff_text, file_path, line_codes
                        )

        except Exception as e:
            logger.warning(f"Could not extract line codes from MR: {e}")

        return line_codes

    def _parse_diff_for_real_line_codes(
        self, diff_text: str, file_path: str, line_codes: dict
    ) -> None:
        """Parse diff text to extract GitLab-format line codes."""
        lines = diff_text.split("\n")
        old_line_num = 0
        new_line_num = 0

        for line in lines:
            if line.startswith("@@"):
                import re

                match = re.search(r"@@\s*-(\d+)(?:,\d+)?\s*\+(\d+)(?:,\d+)?\s*@@", line)
                if match:
                    old_line_num = int(match.group(1)) - 1
                    new_line_num = int(match.group(2)) - 1
            elif line.startswith("+") and not line.startswith("+++"):
                new_line_num += 1
                line_code = self._generate_line_code(
                    file_path, old_line_num, new_line_num
                )
                line_codes[file_path][new_line_num] = line_code
            elif line.startswith("-") and not line.startswith("---"):
                old_line_num += 1
            elif line.startswith(" "):
                old_line_num += 1
                new_line_num += 1
                line_code = self._generate_line_code(
                    file_path, old_line_num, new_line_num
                )
                line_codes[file_path][new_line_num] = line_code

    def _generate_line_code(self, file_path: str, old_line: int, new_line: int) -> str:
        """Generate a line code in GitLab format: SHA_old_new."""
        import hashlib

        file_hash = hashlib.sha1(file_path.encode()).hexdigest()[:8]
        return f"{file_hash}_{old_line}_{new_line}"

    def _get_line_code_for_comment(
        self, line_codes: dict[str, dict[int, str]], file_path: str, line_number: int
    ) -> Optional[str]:
        """Get the line code for a specific file and line number."""
        if file_path in line_codes and line_number in line_codes[file_path]:
            return line_codes[file_path][line_number]

        return self._generate_line_code(file_path, line_number, line_number)

    def post_review_to_gitlab(self, review_result: ReviewResults) -> bool:
        """Post the automated review to GitLab as comments."""
        try:
            mr = self.gitlab_client.get_merge_request(mr_id=review_result.mr_id)
            if not mr:
                logger.error(f"Could not find MR {review_result.mr_id}")
                return False

            project = self.gitlab_client.gl.projects.get(self.project_id)
            mr_obj = project.mergerequests.get(mr.iid)

            summary_comment = self._format_summary_comment(review_result)
            mr_obj.notes.create({"body": summary_comment})

            if review_result.inline_comments:
                line_codes = self._extract_line_codes_from_mr(mr_obj)

                for comment in review_result.inline_comments:
                    inline_comment = self._format_inline_comment(comment)

                    line_code = self._get_line_code_for_comment(
                        line_codes, comment.file_path, comment.line_number
                    )

                    try:
                        discussion_data = {
                            "body": inline_comment,
                            "position": {
                                "base_sha": mr.diff_refs["base_sha"],
                                "start_sha": mr.diff_refs["start_sha"],
                                "head_sha": mr.diff_refs["head_sha"],
                                "old_path": comment.file_path,
                                "new_path": comment.file_path,
                                "new_line": comment.line_number,
                                "position_type": "text",
                                "line_range": {
                                    "start": {
                                        "line_code": line_code,
                                        "type": "new",
                                    },
                                    "end": {
                                        "line_code": line_code,
                                        "type": "new",
                                    },
                                },
                            },
                        }
                        mr_obj.discussions.create(discussion_data)
                    except Exception as e:
                        logger.warning(
                            f"Could not post inline comment, using fallback: {e}"
                        )
                        fallback_comment = f"**{comment.file_path}:{comment.line_number}**\n\n{inline_comment}"
                        mr_obj.notes.create({"body": fallback_comment})

            logger.info(f"Successfully posted review for MR {review_result.mr_id}")
            return True

        except Exception as e:
            logger.error(f"Error posting review to GitLab: {e}")
            return False

    def _format_summary_comment(self, review_result: ReviewResults) -> str:
        """Format the summary comment for GitLab."""
        comment_parts = [
            "## *[NEIA]* Automated Code Review",
            "",
            "*Ceci est une revue automatique par un Assistant IA.*",
            "",
            f"**Score Global: {review_result.overall_score:.1f}/10**",
            "",
            review_result.summary or "",
            "",
        ]

        if review_result.quality_metrics:
            comment_parts.extend(
                [
                    "### Métriques de Qualité",
                    "",
                ]
            )
            for metric in review_result.quality_metrics:
                comment_parts.append(
                    f"- **{metric.name}**: {metric.score:.1f}/10 - {metric.description}"
                )
                if metric.issues:
                    for issue in metric.issues:
                        comment_parts.append(f"  - {issue}")
            comment_parts.append("")

        if review_result.definition_of_done_issues:
            comment_parts.extend(
                [
                    "### Definition of Done",
                    "",
                    "❌ **Issues trouvés:**",
                ]
            )
            for issue in review_result.definition_of_done_issues:
                comment_parts.append(f"- {issue}")
            comment_parts.append("")

        if review_result.recommendations:
            comment_parts.extend(
                [
                    "### Recommandations",
                    "",
                ]
            )
            for rec in review_result.recommendations:
                comment_parts.append(f"- {rec}")
            comment_parts.append("")

        comment_parts.extend(
            [
                "---",
                f"*Analyse générée le {review_result.analysis_timestamp}*",
            ]
        )

        return "\n".join(comment_parts)

    def _format_inline_comment(self, comment: ReviewComment) -> str:
        """Format an inline comment following conventional comments format."""
        decorations = []
        if isinstance(comment.severity, ReviewCommentSeverity):
            if comment.severity == ReviewCommentSeverity.CRITICAL:
                decorations.append("blocking")
            else:
                decorations.append("non-blocking")

        decoration_str = ""
        if decorations:
            decoration_str = f" ({','.join(decorations)})"

        comment_header = f"*[NEIA]* **{comment.comment_type.value}**{decoration_str}: {comment.message}"

        comment_parts = [comment_header]

        if comment.suggestion:
            comment_parts.extend(
                [
                    "",
                    comment.suggestion,
                ]
            )

        return "\n".join(comment_parts)
