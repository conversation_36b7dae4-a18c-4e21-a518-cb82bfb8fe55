import os
import re
from pathlib import Path
from typing import Optional

import click
import git
from gitlab.v4.objects import CurrentUser, ProjectMergeRequest
from InquirerPy import inquirer
from InquirerPy.base.control import Choice

from neia.aider_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Aider<PERSON><PERSON><PERSON>, ManagedCoder
from neia.gitlab_client import GitLabClient
from neia.helpers import resume_mr_discussion, try_parse_int
from neia.local_repository_manager import LocalRepositoryManager
from neia.models import AnalysisResult, NeiaIssue


class RepositoryAnalyzer:
    def __init__(
        self,
        project_name: str,
        git_url: str,
        model_name: str,
        base_path: str = "projects",
    ):
        self.project_name = project_name
        self.git_url = git_url
        self.model_name = model_name
        self.repo_manager = LocalRepositoryManager(base_path)

    def get_repo(self) -> git.Repo:
        """Get the Git repository instance."""
        return self.repo_manager.get_repo(self.project_name)

    def ensure_local_repository(self) -> git.Repo:
        """Ensures the repository exists locally and is up to date."""
        return self.repo_manager.ensure_local_repository(
            self.project_name, self.git_url
        )

    def ensure_branch(self, branch_name: str) -> None:
        """Ensure we're on the specified branch and it's up to date."""
        repo = self.get_repo()
        self.repo_manager.ensure_branch(repo, branch_name)

    def generate_prompt(
        self,
        repo: git.Repo,
        branch_name: str,
        issue: Optional[NeiaIssue],
        mr_description: Optional[str] = None,
        mr_discussion: Optional[str] = None,
    ) -> str:
        # Get detailed commit history for context
        commit_history = repo.git.log(
            "--pretty=format:%h %s%nAuthor: %an <%ae>%nDate: %ad%n%n%b%nModified files:",
            "--name-only",
            "--no-merges",
            "--reverse",
            "--date=iso",
            f"origin/main..{branch_name}",  # Only commits in this branch not in main
        )

        prompt_parts = []

        if issue:
            prompt_parts.append(f"Issue title: {issue.title}")
            prompt_parts.append(f"Issue description: {issue.description}")

            # Comments on the original issue
            issue_comments = "\n".join(
                f"{comment.author}: {comment.body}" for comment in issue.comments
            )

            # Ajouter les commentaires de l'issue s'ils existent
            if issue_comments:
                prompt_parts.append("\nComments made for this issue:")
                prompt_parts.append(issue_comments)

        # Ajouter l'historique des commits s'il existe
        if commit_history:
            prompt_parts.append("\nPrevious changes made for this issue:")
            prompt_parts.append(commit_history)

        # Description de la MR s'il existe
        if mr_description:
            prompt_parts.append("\nOriginal merge request made for this issue:")
            prompt_parts.append(mr_description)

        # Ajouter les commentaires du MR s'ils existent
        if mr_discussion:
            prompt_parts.append("\nDiscussion for this merge request:")
            prompt_parts.append(mr_discussion)

        # Ajouter le statut actuel
        prompt_parts.append("\nCurrent status:")
        prompt_parts.append(f"- Branch: {branch_name}")
        prompt_parts.append(
            f"- Last commit: {repo.head.commit.hexsha[:7]} ({str(repo.head.commit.message).splitlines()[0]})"
        )

        return "\n".join(prompt_parts)

    def get_coder(
        self, repo: git.Repo, edit_format: Optional[str] = None
    ) -> ManagedCoder:
        repo_path = repo.working_dir

        if not repo_path:
            raise ValueError("Repository must have a working directory")

        convention_file = Path(repo_path) / ".neia/CONVENTIONS.md"

        config = AiderCoderConfig(
            model_name=self.model_name,
            edit_format=edit_format,
            detect_urls=False,
            attribute_author=False,
            attribute_committer=False,
            yes_always=True,
            conventions_file_path=(
                str(convention_file) if convention_file.exists() else None
            ),
        )

        return AiderManager.create_coder(str(repo_path), config, self.project_name)

    def get_result(
        self,
        repo: git.Repo,
        base_prompt: str,
        discussion_id: Optional[str],
        fix_errors: bool,
        prompt_only: bool,
        bypass_action_plan: bool = False,
    ) -> AnalysisResult:
        try:
            coder = self.get_coder(repo)
            ask_coder = self.get_coder(repo, edit_format="ask")

            ask_coder.enable_technology_level()

            complexity_prompt = (
                base_prompt
                + "\n\nEstimate the complexity of this task on the scale 0-10, in your response give only the number."
            )

            complexity_response = ask_coder.run_single(complexity_prompt)

            complexity = try_parse_int(complexity_response)

            if complexity:
                print(f"\n\nEstimated complexity of the task: {complexity}/10")

            should_execute = None

            if not bypass_action_plan and (complexity is None or complexity > 2):
                criteria_simple_request = [
                    "Demandes de moins de 100 mots",
                    "Actions laborieuses ou formattage : corrige les typo, ajoute des commentaires, supprime ce module, indente ce code, ajoute des espaces etc…",
                    "Demandes de documentation simple : documente telle partie de l’application",
                    "Corrections simples : problème de syntaxe, bug ciblé avec stacktrace",
                ]

                simple_request_prompt = (
                    base_prompt
                    + "\n\nAnswer only with YES/NO whether the request is simple, using exclusively these criteria:\n"
                    # + "\n\nAnswer with YES/NO whether the request is simple and justify your response and state which of the following criteria was used, using exclusively these criteria:\n"
                    + "\n".join(f"- {item}" for item in criteria_simple_request)
                )

                simple_request_response = ask_coder.run_single(simple_request_prompt)

                if not simple_request_response == "YES":
                    should_execute = False

                    with open("neia/templates/plan.txt") as file:
                        plan_template = file.read()

                    plan_prompt = (
                        base_prompt
                        + "\n\nPresent a plan in this format:\n\n"
                        + plan_template
                    )

                    ask_coder.run_single(plan_prompt)

                    for _i in range(5):
                        should_execute = inquirer.select(
                            message="Select an action:",
                            choices=[
                                Choice(True, name="Execute this plan"),
                                Choice(False, name="Modify this plan"),
                            ],
                        ).execute()

                        if should_execute:
                            break

                        plan_update = inquirer.text(
                            message="Write your comment:",
                        ).execute()

                        update_prompt = (
                            base_prompt
                            + "\n\nUpdate the plan according to this comment:\n\n"
                            + plan_update
                        )

                        ask_coder.run_single(update_prompt)

                    if not should_execute:
                        return AnalysisResult(
                            coder_response=None,
                            discussion_id=discussion_id,
                            error="Try to reformulate the initial issue.",
                        )

            prompt = (
                base_prompt
                + "\nWhat changes should be made next to complete the task "
                + (
                    ""
                    if should_execute is None
                    else "according to the generated plan including its modifications "
                )
                + "(without impacting the rest of the code)? You must begin your response with '*[NEIA]*'."
            )

            # Include detailed context in the prompt
            if prompt_only:
                return AnalysisResult(
                    coder_response=None, error=prompt, discussion_id=discussion_id
                )
            else:
                if fix_errors:
                    ask_prompt = (
                        base_prompt
                        + "\nReply in French what was the pipeline error and how did you fix it."
                    )
                else:
                    answer_format = [
                        "*[NEIA]* J'ai pris en compte votre commentaire sur [sujet spécifique].",
                        "Le problème était [explication concise du problème].",
                        "J'ai [description de l'action] en [explication du changement spécifique] parce que [justification].",
                        "Ce changement permet [bénéfice/résultat].",
                    ]
                    ask_prompt = (
                        base_prompt
                        + "\nReply in French using this format:\n "
                        + "\n".join(answer_format)
                    )
                discussion_response = ask_coder.run_single(ask_prompt)
                click.echo(f"Running analysis with context:\n{prompt}")
                coder_response = coder.run_single(prompt)
                click.echo(f"Analysis response:\n{coder_response}")
                return AnalysisResult(
                    coder_response=discussion_response,
                    commit_id=repo.head.commit.hexsha[:7],
                    discussion_id=discussion_id,
                )
        except Exception as e:
            click.echo(f"Error during aider analysis: {e}")
            return AnalysisResult(
                coder_response=None, discussion_id=discussion_id, error=str(e)
            )

    def analyze(
        self,
        issue: Optional[NeiaIssue],
        mr: Optional[ProjectMergeRequest],
        branch_name: str,
        pipeline_error: Optional[str],
        user: CurrentUser,
        prompt_only: bool = False,
        bypass_action_plan: bool = False,
    ) -> list[AnalysisResult]:
        """
        Analyze the repository based on the issue description
        and apply changes using aider. Returns details about changes that were made.
        """
        repo = self.ensure_local_repository()

        self.ensure_branch(branch_name)

        results = []

        # If the MR is to be created
        if not mr:
            base_prompt = self.generate_prompt(repo, branch_name, issue)
            if not prompt_only:
                ask_coder = self.get_coder(repo, edit_format="ask")
                folder_path = Path(".gitlab/merge_request_templates")
                description_format = ""
                if folder_path.exists():
                    description_format = (
                        " using the format "
                        + " or ".join(
                            [f.name for f in folder_path.iterdir() if f.is_file()]
                        )
                        + " (choose the appropriate one) in .gitlab/merge_request_templates"
                    )
                prompt = (
                    base_prompt
                    + "\n\nFormulate a title and a description for the given issue in French"
                    + description_format
                    + "."
                    + (f"Include this issue reference: {issue.url}" if issue else "")
                    + ". Give your response in form <title></title><description></description>."
                    + f"You must conclude the Merge Request description with '*Cette MR a été générée par NEIA - Assistant IA pour {user.attributes.get('name')}'*."
                )
                response = ask_coder.run_single(prompt)
                if isinstance(response, str):
                    match = re.search(
                        r"<title>(.*?)</title>.*?<description>(.*?)</description>",
                        response,
                        re.DOTALL,
                    )
                else:
                    match = None
                if match:
                    title = match.group(1).strip()
                    description = match.group(2).strip()
                    results.append(
                        AnalysisResult(
                            coder_response=description,
                            title=title,
                        )
                    )
            results.append(
                self.get_result(
                    repo, base_prompt, None, False, prompt_only, bypass_action_plan
                )
            )
        # If the MR already exists and there is a pipeline error
        elif pipeline_error:
            prompt = f"Last pipeline contains this error:\n{pipeline_error}\n\nWhat changes should be made next to fix it? You must begin your response with '*[NEIA]*'."
            results.append(
                self.get_result(
                    repo, prompt, None, True, prompt_only, bypass_action_plan
                )
            )
        # If the MR already exists and there are no pipeline errors
        else:
            # Loop on all threads
            for discussion_data in mr.discussions.list():
                discussion = resume_mr_discussion(mr, discussion_data)
                if discussion:
                    prompt = self.generate_prompt(
                        repo, branch_name, issue, mr.description, discussion
                    )
                    results.append(
                        self.get_result(
                            repo,
                            prompt,
                            discussion_data.id,
                            False,
                            prompt_only,
                            bypass_action_plan,
                        )
                    )
        return results


def analyze(
    gitlab_client: GitLabClient,
    project_id: str,
    branch_name: str,
    issue: Optional[NeiaIssue],
    mr: Optional[ProjectMergeRequest],
    dry_run: bool,
    prompt_only: bool,
    bypass_action_plan: bool = False,
) -> None:
    project = gitlab_client.get_project(project_id)
    user = gitlab_client.get_user()
    if not user:
        raise ValueError("Unable to authenticate with GitLab - user not found")
    llm_model = os.getenv("LLM_MODEL", "gpt-4o-mini")
    analyzer = RepositoryAnalyzer(project.name, project.git_url, llm_model)

    # Get pipeline error if there is any
    pipeline_error = gitlab_client.get_pipeline_error(mr) if mr else None

    # Analyze and apply changes
    results = analyzer.analyze(
        issue,
        mr,
        branch_name,
        pipeline_error,
        prompt_only=prompt_only,
        user=user,
        bypass_action_plan=bypass_action_plan,
    )
    repo = analyzer.get_repo()

    if not results:
        click.echo("No changes to be done")
        return

    if not (dry_run or prompt_only):
        repo.git.push()

    for result in results:
        if result.error is None:
            if dry_run:
                click.echo(
                    f"Dry run mode - check changes in the repository at path: {str(repo.working_dir)}"
                )
                if result.commit_id:
                    click.echo("Commit: " + result.commit_id)
                click.echo(result.coder_response)
            else:
                click.echo("Changes were applied successfully")
                title = (
                    f"Changes for {issue.source} issue #{issue.id}"
                    if issue
                    else "Changes for issue"
                )
                description = (
                    result.coder_response if result.coder_response is not None else ""
                )
                mr_url = gitlab_client.submit_review_changes(
                    project_id,
                    result.discussion_id,
                    result.commit_id,
                    repo.active_branch.name,
                    result.title or title,
                    description,
                )
                click.echo(f"Merge request: {mr_url}")
        else:
            click.echo("An error occurred")
            click.echo(result)
            click.echo(result.error)
