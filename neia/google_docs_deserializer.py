from typing import Optional

from pydantic import ConfigDict, Field, field_validator
from pydantic.dataclasses import dataclass
from typing_extensions import NotRequired, TypedDict


# Google Docs API response type definitions
class TextRunData(TypedDict):
    content: NotRequired[str]


class ParagraphElementData(TypedDict):
    textRun: NotRequired[TextRunData]


class ParagraphData(TypedDict):
    elements: NotRequired[list[ParagraphElementData]]


class ContentElementData(TypedDict):
    paragraph: NotRequired[ParagraphData]


class DocumentBodyData(TypedDict):
    content: NotRequired[list[ContentElementData]]


class GoogleDocumentData(TypedDict):
    documentId: NotRequired[str]
    title: NotRequired[str]
    body: NotRequired[DocumentBodyData]


# Pydantic dataclass configuration
default_config = ConfigDict(
    str_strip_whitespace=False,  # Preserve whitespace in content
    validate_assignment=True,  # Validate on assignment
    frozen=False,  # Allow mutation for flexibility
    arbitrary_types_allowed=True,  # Allow custom types
)


# Pydantic dataclasses for structured data
@dataclass(config=default_config)
class TextRun:
    """Represents a text run in a Google Doc paragraph element."""

    content: str = Field(
        default="", description="The text content of the run", min_length=0
    )

    @field_validator("content", mode="before")
    @classmethod
    def validate_content(cls, v: str) -> str:
        """Ensure content is a string (Google API sometimes returns other types)."""
        return str(v) if v is not None else ""


@dataclass(config=default_config)
class ParagraphElement:
    """Represents an element within a paragraph (can contain text runs, etc.)."""

    textRun: Optional[TextRun] = Field(
        default=None, description="Text run element if this element contains text"
    )

    def get_text_content(self) -> str:
        """Extract text content from this element."""
        return self.textRun.content if self.textRun else ""


@dataclass(config=default_config)
class Paragraph:
    """Represents a paragraph containing multiple elements."""

    elements: list[ParagraphElement] = Field(
        default_factory=list, description="List of paragraph elements"
    )

    def get_text_content(self) -> str:
        """Extract all text content from this paragraph."""
        return " ".join(
            element.get_text_content()
            for element in self.elements
            if element.get_text_content()
        )


@dataclass(config=default_config)
class ContentElement:
    """Represents a content element in a Google Doc (can be paragraph, table, etc.)."""

    paragraph: Optional[Paragraph] = Field(
        default=None, description="Paragraph content if this element is a paragraph"
    )

    def get_text_content(self) -> str:
        """Extract text content from this content element."""
        return self.paragraph.get_text_content() if self.paragraph else ""


class GoogleDocsDeserializer:
    """Deserializer for Google Docs API responses into structured Pydantic dataclass objects."""

    @staticmethod
    def deserialize_text_run(data: TextRunData) -> TextRun:
        """Deserialize a TextRun from API data."""
        return TextRun(content=data.get("content", ""))

    @staticmethod
    def deserialize_paragraph_element(data: ParagraphElementData) -> ParagraphElement:
        """Deserialize a ParagraphElement from API data."""
        text_run = None
        if "textRun" in data:
            text_run = GoogleDocsDeserializer.deserialize_text_run(data["textRun"])
        return ParagraphElement(textRun=text_run)

    @staticmethod
    def deserialize_paragraph(data: ParagraphData) -> Paragraph:
        """Deserialize a Paragraph from API data."""
        elements = []
        for element_data in data.get("elements", []):
            elements.append(
                GoogleDocsDeserializer.deserialize_paragraph_element(element_data)
            )
        return Paragraph(elements=elements)

    @staticmethod
    def deserialize_content_element(data: ContentElementData) -> ContentElement:
        """Deserialize a ContentElement from API data."""
        paragraph = None
        if "paragraph" in data:
            paragraph = GoogleDocsDeserializer.deserialize_paragraph(data["paragraph"])
        return ContentElement(paragraph=paragraph)

    @staticmethod
    def deserialize_content(
        raw_content: list[ContentElementData],
    ) -> list[ContentElement]:
        """Deserialize a list of content elements from API data."""
        return [
            GoogleDocsDeserializer.deserialize_content_element(element_data)
            for element_data in raw_content
        ]
