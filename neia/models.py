from dataclasses import dataclass
from enum import Enum
from typing import Optional, Union


@dataclass
class IssueComment:
    id: int
    author: str
    body: str


@dataclass
class NeiaIssue:
    id: Union[str, int]
    source: str
    title: str
    description: str
    comments: list[IssueComment]
    url: Optional[str] = None


class ReviewCommentType(Enum):
    """Types of review comments following conventional comments format."""

    PRAISE = "praise"
    NITPICK = "nitpick"
    SUGGESTION = "suggestion"
    ISSUE = "issue"
    QUESTION = "question"
    THOUGHT = "thought"
    CHORE = "chore"


class ReviewCommentSeverity(Enum):
    """Severity of the impact mentioned in a review comment."""

    MINOR = "minor"
    MAJOR = "major"
    CRITICAL = "critical"


@dataclass
class ReviewComment:
    """Represents a single code review comment."""

    file_path: str
    line_number: int
    comment_type: ReviewCommentType
    message: str
    severity: Optional[ReviewCommentSeverity] = None
    suggestion: Optional[str] = None


@dataclass
class QualityMetric:
    """Represents a quality assessment metric."""

    name: str
    score: float  # 0-10
    description: str
    issues: list[str]


@dataclass
class AnalysisResult:
    """Result class for repository analysis."""

    coder_response: Optional[str]
    title: Optional[str] = None
    commit_id: Optional[str] = None
    discussion_id: Optional[str] = None
    error: Optional[str] = None


@dataclass
class ReviewResults:
    """Result class for code review analysis."""

    mr_id: str
    mr_iid: str
    overall_score: float  # 0-10
    summary: Optional[str] = None
    inline_comments: Optional[list[ReviewComment]] = None
    quality_metrics: Optional[list[QualityMetric]] = None
    definition_of_done_issues: Optional[list[str]] = None
    recommendations: Optional[list[str]] = None
    analysis_timestamp: Optional[str] = None
