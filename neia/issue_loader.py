import os
import re

from neia.gitlab_client import GitLabClient
from neia.google_client import GoogleClient
from neia.logging.neia_logging_adapter import get_logger
from neia.models import NeiaIssue
from neia.tuleap_client import TuleapClient

logger = get_logger("issue_loader")


def get_issue_from_url(
    issue_url: str, project_id: str, should_assign_issue: bool = False
) -> NeiaIssue:
    logger.debug(
        f"Starting get_issue_from_url with URL: {issue_url}, project_id: {project_id}"
    )

    gitlab_url = os.getenv("GITLAB_URL")
    gitlab_token = os.getenv("GITLAB_TOKEN")
    tuleap_api_url = os.getenv("TULEAP_API_URL")
    tuleap_user = os.getenv("TULEAP_USER")
    tuleap_password = os.getenv("TULEAP_PASSWORD")
    tuleap_description_field_id = os.getenv("TULEAP_DESCRIPTION_FIELD_ID")
    tuleap_assignee_field_id = os.getenv("TULEAP_ASSIGNEE_FIELD_ID")
    google_token_file = os.getenv("GOOGLE_TOKEN_FILE", "token.json")

    logger.debug(
        f"Environment variables loaded - GitLab URL: {'set' if gitlab_url else 'not set'}, "
        f"GitLab token: {'set' if gitlab_token else 'not set'}, "
        f"Tuleap API URL: {'set' if tuleap_api_url else 'not set'}, "
        f"Tuleap user: {'set' if tuleap_user else 'not set'}, "
        f"Tuleap password: {'set' if tuleap_password else 'not set'}, "
        f"Tuleap description field ID: {'set' if tuleap_description_field_id else 'not set'}, "
        f"Tuleap assignee field ID: {'set' if tuleap_assignee_field_id else 'not set'}, "
        f"Google token file: {google_token_file}"
    )

    issue = None

    if gitlab_url and gitlab_token:
        logger.debug("GitLab credentials available, checking for GitLab URL pattern")
        gitlab_pattern = r"(" + re.escape(gitlab_url) + r".+/-/issues/(\d+))"
        logger.debug(f"GitLab pattern: {gitlab_pattern}")
        gitlab_match = re.search(gitlab_pattern, issue_url)
        if gitlab_match:
            issue_id = gitlab_match.group(2)
            logger.debug(
                f"GitLab URL pattern matched - Issue ID: {issue_id}, Full match: {gitlab_match.group(1)}"
            )
            # Get GitLab issue
            logger.debug(f"Initializing GitLab client with URL: {gitlab_url}")
            gitlab_client = GitLabClient(gitlab_url, gitlab_token, project_id)
            logger.debug(f"Fetching GitLab issue {issue_id} from project {project_id}")
            issue = gitlab_client.get_issue(project_id, issue_id)
            if should_assign_issue:
                gitlab_client.assign_issue(project_id, issue_id)
            issue.url = gitlab_match.group(1)
        else:
            logger.debug("URL does not match GitLab pattern")
    else:
        logger.debug("GitLab credentials not available, skipping GitLab check")

    gdocs_pattern = (
        r"(https?://docs\.google\.com/document/d/([a-zA-Z0-9_-]+)(/[^?)]+)?(\?[^)]+)?)"
    )
    logger.debug(f"Google Docs pattern: {gdocs_pattern}")
    gdocs_match = re.search(gdocs_pattern, issue_url)
    if not issue and gdocs_match:
        doc_id = gdocs_match.group(2)
        logger.debug(
            f"Google Docs URL pattern matched - Document ID: {doc_id}, Full match: {gdocs_match.group(1)}"
        )
        logger.debug(f"Initializing Google client with token file: {google_token_file}")
        google_client = GoogleClient(google_token_file)
        logger.debug("Attempting Google authentication")
        auth_success = google_client.auth()
        if not auth_success:
            raise Exception(
                "The Google token does not exist or is not valid. Generate it by running 'poetry run neia generate-google-token'."
            )
        logger.debug("Google authentication successful")
        logger.debug(f"Fetching Google Doc issue with document ID: {doc_id}")
        issue = google_client.get_issue(doc_id)
        issue.url = gdocs_match.group(1)
    elif not issue:
        logger.debug("URL does not match Google Docs pattern or issue already found")

    if not issue and tuleap_api_url:
        tuleap_pattern = (
            r"(" + re.escape(tuleap_api_url) + r"/plugins/tracker/\?aid=(\d+))"
        )
        logger.debug(f"Tuleap pattern: {tuleap_pattern}")
        tuleap_match = re.search(tuleap_pattern, issue_url)
        if tuleap_match:
            artifact_id = tuleap_match.group(2)
            logger.debug(
                f"Tuleap URL pattern matched - Artifact ID: {artifact_id}, Full match: {tuleap_match.group(1)}"
            )
            if (
                not tuleap_api_url
                or not tuleap_user
                or not tuleap_password
                or not tuleap_description_field_id
                or not tuleap_assignee_field_id
            ):
                logger.error(
                    "Tuleap credentials incomplete - missing required environment variables"
                )
                raise Exception(
                    "Please provide 'TULEAP_API_URL', 'TULEAP_USER', 'TULEAP_PASSWORD' and 'TULEAP_DESCRIPTION_FIELD_ID'."
                )
            logger.debug(
                f"Initializing Tuleap client with API URL: {tuleap_api_url}, user: {tuleap_user}, description field ID: {tuleap_description_field_id}"
            )
            tuleap_client = TuleapClient(
                tuleap_api_url,
                tuleap_user,
                tuleap_password,
                int(tuleap_description_field_id),
                int(tuleap_assignee_field_id),
            )
            logger.debug(f"Fetching Tuleap issue with artifact ID: {artifact_id}")
            issue = tuleap_client.get_issue(artifact_id)
            if should_assign_issue:
                logger.debug(
                    f"Assigning issue {artifact_id} to {tuleap_user} in Tuleap"
                )
                tuleap_client.assign_issue(artifact_id)
            issue.url = tuleap_match.group(1)
        else:
            logger.debug("URL does not match Tuleap pattern")
    elif not issue:
        logger.debug("Tuleap API URL not available, skipping Tuleap check")

    if not issue:
        raise Exception("The issue URL is not valid. Please provide a valid issue URL.")

    logger.debug(
        f"Successfully completed get_issue_from_url - returning issue: {issue.title if hasattr(issue, 'title') else 'Unknown title'}"
    )
    return issue
