from typing import Optional, Union

import requests
from pydantic import ConfigDict, Field
from pydantic.dataclasses import dataclass
from requests.auth import HTTPBasicAuth

from neia.models import NeiaIssue

# Pydantic dataclass configuration
default_config = ConfigDict(
    str_strip_whitespace=True,
    validate_assignment=True,
    frozen=False,
    arbitrary_types_allowed=True,
)


@dataclass(config=default_config)
class TuleapAssignResponse:
    """Response from Tuleap artifact assignment API."""

    success: bool = Field(description="Whether the assignment was successful")
    status_code: int = Field(description="HTTP status code")
    artifact_id: Optional[str] = Field(
        default=None, description="ID of the assigned artifact"
    )
    message: Optional[str] = Field(default=None, description="Response message if any")


@dataclass(config=default_config)
class TuleapToken:
    """Represents a Tuleap authentication token."""

    user_id: int = Field(description="User ID associated with the token")
    token: str = Field(description="Authentication token string")
    uri: str = Field(description="URI associated with the token")


class TuleapClient:
    def __init__(
        self,
        host: str,
        username: str,
        password: str,
        description_field_id: int,
        assignee_field_id: int,
    ) -> None:
        self.host = host
        self.username = username
        self.password = password
        self.description_field_id = description_field_id
        self.assignee_field_id = assignee_field_id

    def get_basic_auth(self) -> HTTPBasicAuth:
        return HTTPBasicAuth(self.username, self.password)

    def _get_token(self) -> TuleapToken:
        response = requests.post(
            f"{self.host}/api/tokens",
            headers=self.get_headers(),
            json={"username": self.username, "password": self.password},
        )
        content = response.json()
        return TuleapToken(
            user_id=content["user_id"],
            token=content["token"],
            uri=content["uri"],
        )

    def get_headers(self, token: Optional[TuleapToken] = None) -> dict[str, str]:
        headers = {"Content-Type": "application/json"}
        if token:
            headers["X-Auth-Token"] = token.token
            headers["X-Auth-UserId"] = str(token.user_id)
        return headers

    # Not used for the moment
    def get_artifacts(self, tracker_id: int) -> list[dict[str, Union[str, int]]]:
        response = requests.get(
            f"{self.host}/api/trackers/{tracker_id}/artifacts",
            headers=self.get_headers(),
            auth=self.get_basic_auth(),
        )
        return response.json()

    def assign_issue(self, artifact_id: str) -> TuleapAssignResponse:
        token = self._get_token()
        response = requests.put(
            f"{self.host}/api/artifacts/{artifact_id}",
            headers=self.get_headers(token),
            auth=self.get_basic_auth(),
            json={
                "id": artifact_id,
                "values": [
                    {
                        "field_id": self.assignee_field_id,
                        "bind_value_ids": [token.user_id],
                    }
                ],
            },
        )

        response.raise_for_status()

        if response.content.strip():
            # Parse the actual API response and extract relevant fields
            api_response = response.json()
            return TuleapAssignResponse(
                success=True,
                status_code=response.status_code,
                artifact_id=str(api_response.get("id", artifact_id)),
                message=api_response.get("message"),
            )
        else:
            return TuleapAssignResponse(
                success=True, status_code=response.status_code, artifact_id=artifact_id
            )

    def get_issue(self, artifact_id: str) -> NeiaIssue:
        response = requests.get(
            f"{self.host}/api/artifacts/{artifact_id}",
            headers=self.get_headers(),
            auth=self.get_basic_auth(),
        )
        artifact = response.json()
        if "error" in artifact:
            raise Exception(
                "Error while fetching issue from Tuleap: " + artifact["error"]
            )
        values = artifact["values"]
        description = next(
            (
                item["value"]
                for item in values
                if item["field_id"] == self.description_field_id
            ),
            None,
        )
        if description and "<" in description:
            # If description contains HTML tags, convert to plain text
            from bs4 import BeautifulSoup

            soup = BeautifulSoup(description, "html.parser")
            description = soup.get_text()
        return NeiaIssue(
            id=artifact["id"],
            source="tuleap",
            title=artifact["title"],
            description=description or "",
            comments=[],
        )
