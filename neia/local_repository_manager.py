"""Repository utilities for managing Git repositories."""

from pathlib import Path

import git

from neia.logging.neia_logging_adapter import get_logger

logger = get_logger("RepositoryUtils")


class LocalRepositoryManager:
    """Utility class for managing local Git repositories."""

    def __init__(self, base_path: str = "projects"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)

    def get_repo_path(self, project_name: str) -> Path:
        """Get the local path for a project repository."""
        return self.base_path / project_name

    def get_repo(self, project_name: str) -> git.Repo:
        """Get the Git repository instance."""
        repo_path = self.get_repo_path(project_name)
        return git.Repo(str(repo_path))

    def ensure_local_repository(self, project_name: str, git_url: str) -> git.Repo:
        """Ensures the repository exists locally and is up to date."""
        repo_path = self.get_repo_path(project_name)

        if not repo_path.exists():
            logger.info(f"Cloning repository {git_url} to {repo_path}")
            repo = git.Repo.clone_from(git_url, str(repo_path))
        else:
            logger.info(f"Updating existing repository at {repo_path}")
            repo = git.Repo(str(repo_path))
            origin = repo.remotes.origin

            try:
                origin.fetch()

                remote_refs = [ref.name for ref in origin.refs]
                current_branch = repo.active_branch.name
                remote_branch_path = f"origin/{current_branch}"

                if remote_branch_path in remote_refs:
                    origin.pull()
            except git.GitCommandError:
                repo.git.reset("--hard", f"origin/{repo.active_branch.name}")

        repo.config_writer().set_value("user", "name", "NEIA").release()
        repo.config_writer().set_value(
            "user", "email", "<EMAIL>"
        ).release()
        repo.config_writer().set_value("push", "autoSetupRemote", True).release()

        return repo

    def ensure_branch(self, repo: git.Repo, branch_name: str) -> None:
        """Ensure we're on the specified branch and it's up to date."""
        logger.info(f"Ensuring repository is on branch: {branch_name}")

        remote_branches = [b.strip() for b in repo.git.branch("-r").split("\n")]
        remote_branch = f"origin/{branch_name}"

        if remote_branch in remote_branches:
            if branch_name not in repo.heads:
                logger.info(
                    f"Creating local branch {branch_name} tracking {remote_branch}"
                )
                repo.git.checkout("-b", branch_name, remote_branch)
            else:
                logger.info(f"Switching to existing branch {branch_name}")
                repo.git.checkout(branch_name)

            repo.git.reset("--hard", remote_branch)
        else:
            logger.info(f"Creating new branch {branch_name} from main")
            repo.git.checkout("main")
            repo.git.pull("origin", "main")
            if branch_name in repo.heads:
                repo.git.branch("-D", branch_name)
            repo.create_head(branch_name)
            repo.git.checkout(branch_name)

    def get_conventions_file_path(self, project_name: str) -> Path:
        """Get the path to the CONVENTIONS.md file."""
        return Path(self.get_repo_path(project_name)) / ".neia/CONVENTIONS.md"
