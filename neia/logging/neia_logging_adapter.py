import logging
from collections.abc import MutableMapping
from typing import Any


class NeiaLoggingAdapter(logging.LoggerAdapter):
    def process(
        self, msg: str, kwargs: MutableMapping[str, Any]
    ) -> tuple[str, MutableMapping[str, Any]]:
        return f"{kwargs.get('prefix', '')} {msg}", kwargs


def get_logger(prefix: str) -> NeiaLoggingAdapter:
    logger = logging.getLogger("NEIA")
    return NeiaLoggingAdapter(logger, {"prefix": prefix})
