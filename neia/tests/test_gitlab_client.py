from unittest.mock import Mock, patch

import pytest
from gitlab.v4.objects import ProjectMergeRequest
from gitlab.v4.objects.users import CurrentUser

from neia.gitlab_client import GitLabClient, NeiaProjectInfo
from neia.models import NeiaIssue


class TestGitLabClient:
    """Test cases for GitLabClient class."""

    @pytest.fixture
    def mock_gitlab(self) -> Mock:
        """Create a mock GitLab instance."""
        mock_gl = Mock()
        # Configure auth method
        mock_gl.configure_mock(
            **{
                "auth.return_value": None,
                "user": None,
                "projects.get.return_value": Mock(),
                "mergerequests.list.return_value": [],
            }
        )
        return mock_gl

    @pytest.fixture
    def gitlab_client(self, mock_gitlab: Mock) -> GitLabClient:
        """Create a GitLabClient with mocked GitLab."""
        with patch("neia.gitlab_client.gitlab.Gitlab", return_value=mock_gitlab):
            client = GitLabClient(
                url="https://gitlab.com", token="test-token", project_id="123"
            )
            client.gl = mock_gitlab
            return client

    @pytest.fixture
    def mock_user(self) -> Mock:
        """Create a mock CurrentUser."""
        user = Mock(spec=CurrentUser)
        user.attributes = {"id": 42, "name": "Test User"}
        user.id = 42
        return user

    @pytest.fixture
    def mock_project(self) -> Mock:
        """Create a mock GitLab project."""
        project = Mock()
        project.configure_mock(
            **{
                "id": "123",
                "name": "test-project",
                "ssh_url_to_repo": "**************:test/test-project.git",
                "web_url": "https://gitlab.com/test/test-project",
                "description": "Test project description",
                "issues.get.return_value": Mock(),
                "branches.create.return_value": None,
                "mergerequests.get.return_value": Mock(),
                "mergerequests.list.return_value": [],
                "mergerequests.create.return_value": Mock(),
                "pipelines.get.return_value": Mock(),
                "jobs.get.return_value": Mock(),
            }
        )
        return project

    @pytest.fixture
    def mock_issue(self) -> Mock:
        """Create a mock GitLab issue."""
        issue = Mock()

        # Mock notes
        note1 = Mock()
        note1.configure_mock(
            **{
                "id": 101,
                "author": {"name": "Author 1"},
                "body": "First comment",
                "system": False,
            }
        )

        note2 = Mock()
        note2.configure_mock(
            **{
                "id": 102,
                "author": {"name": "Author 2"},
                "body": "Second comment",
                "system": False,
            }
        )

        system_note = Mock()
        system_note.configure_mock(
            **{
                "id": 103,
                "author": {"name": "System"},
                "body": "System note",
                "system": True,
            }
        )

        issue.configure_mock(
            **{
                "iid": 1,
                "title": "Test Issue",
                "description": "Test issue description",
                "assignee_ids": [],
                "save.return_value": None,
                "notes.list.return_value": [note1, note2, system_note],
            }
        )
        return issue

    @pytest.fixture
    def mock_merge_request(self) -> Mock:
        """Create a mock GitLab merge request."""
        mr = Mock(spec=ProjectMergeRequest)
        mr.id = "456"
        mr.iid = 1
        mr.title = "Test MR"
        mr.description = "Test MR description"
        mr.state = "opened"
        mr.source_branch = "feature-branch"
        mr.target_branch = "main"
        mr.assignee_id = 42
        mr.web_url = "https://gitlab.com/test/test-project/-/merge_requests/1"
        mr.project_id = "123"
        mr.created_at = "2023-01-01T00:00:00Z"
        mr.merged_at = None
        mr.closed_at = None
        mr.merge_status = "can_be_merged"
        mr.detailed_merge_status = "mergeable"
        mr.draft = False

        # Mock discussions
        mock_discussions = Mock()
        mock_discussions.list.return_value = []
        mock_discussions.get.return_value = Mock()
        mr.discussions = mock_discussions

        # Mock notes
        mock_notes = Mock()
        mock_notes.create.return_value = Mock()
        mr.notes = mock_notes

        # Mock pipelines
        mock_pipelines = Mock()
        mock_pipelines.list.return_value = []
        mr.pipelines = mock_pipelines

        return mr

    def test_init(self) -> None:
        """Test GitLabClient initialization."""
        with patch("neia.gitlab_client.gitlab.Gitlab") as mock_gitlab_class:
            mock_gl = Mock()
            mock_gitlab_class.return_value = mock_gl

            client = GitLabClient(
                url="https://gitlab.com", token="test-token", project_id="123"
            )

            assert client.project_id == "123"
            mock_gitlab_class.assert_called_once_with(
                "https://gitlab.com", private_token="test-token"
            )

    def test_get_user_success(
        self, gitlab_client: GitLabClient, mock_user: Mock
    ) -> None:
        """Test successful user retrieval."""
        gitlab_client.gl.user = mock_user

        result = gitlab_client.get_user()

        assert result == mock_user

    def test_assign_issue(
        self,
        gitlab_client: GitLabClient,
        mock_project: Mock,
        mock_issue: Mock,
        mock_user: Mock,
    ) -> None:
        """Test assigning an issue to current user."""
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore
        mock_project.issues.get.return_value = mock_issue  # type: ignore
        gitlab_client.gl.user = mock_user

        gitlab_client.assign_issue("123", "1")

        assert mock_issue.assignee_ids == [42]

    def test_get_issue(
        self, gitlab_client: GitLabClient, mock_project: Mock, mock_issue: Mock
    ) -> None:
        """Test retrieving an issue."""
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore
        mock_project.issues.get.return_value = mock_issue  # type: ignore

        result = gitlab_client.get_issue("123", "1")

        assert isinstance(result, NeiaIssue)
        assert result.id == 1
        assert result.source == "gitlab"
        assert result.title == "Test Issue"
        assert result.description == "Test issue description"
        assert len(result.comments) == 2  # Only non-system notes
        assert result.comments[0].id == 101
        assert result.comments[0].author == "Author 1"
        assert result.comments[0].body == "First comment"

    def test_get_project(self, gitlab_client: GitLabClient, mock_project: Mock) -> None:
        """Test retrieving project information."""
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        result = gitlab_client.get_project("123")

        assert isinstance(result, NeiaProjectInfo)
        assert result.id == "123"
        assert result.name == "test-project"
        assert result.git_url == "**************:test/test-project.git"
        assert result.web_url == "https://gitlab.com/test/test-project"
        assert result.description == "Test project description"

    def test_create_branch(
        self, gitlab_client: GitLabClient, mock_project: Mock
    ) -> None:
        """Test creating a new branch."""
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        gitlab_client.create_branch("123", "new-feature", "main")

        mock_project.branches.create.assert_called_once_with(
            {"branch": "new-feature", "ref": "main"}
        )

    def test_get_merge_request_success(
        self, gitlab_client: GitLabClient, mock_merge_request: Mock, mock_user: Mock
    ) -> None:
        """Test successful merge request retrieval."""
        gitlab_client.gl.user = mock_user

        mock_project = Mock()
        mock_project.mergerequests.get.return_value = mock_merge_request  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore
        gitlab_client.gl.mergerequests.list.return_value = [mock_merge_request]  # type: ignore

        result = gitlab_client.get_merge_request(mr_id="456")

        assert result == mock_merge_request

    def test_get_merge_request_not_assigned(
        self, gitlab_client: GitLabClient, mock_merge_request: Mock, mock_user: Mock
    ) -> None:
        """Test merge request retrieval when user is not assigned."""
        gitlab_client.gl.user = mock_user
        mock_merge_request.assignee_id = 999  # Different user

        mock_project = Mock()
        mock_project.mergerequests.get.return_value = mock_merge_request  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        result = gitlab_client.get_merge_request(mr_id="456")

        assert result is None

    def test_get_merge_request_not_found(
        self, gitlab_client: GitLabClient, mock_user: Mock
    ) -> None:
        """Test merge request retrieval when MR doesn't exist."""
        gitlab_client.gl.user = mock_user

        mock_project = Mock()
        mock_project.mergerequests.get.side_effect = Exception("Not found")  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        result = gitlab_client.get_merge_request(mr_id="456")

        assert result is None

    def test_get_merge_request_no_user(self, gitlab_client: GitLabClient) -> None:
        """Test merge request retrieval when user authentication fails."""
        gitlab_client.gl.user = None

        with pytest.raises(Exception, match="User not found"):
            gitlab_client.get_merge_request(mr_id="456")

    def test_get_merge_request_by_branch_found(
        self, gitlab_client: GitLabClient, mock_merge_request: Mock
    ) -> None:
        """Test finding merge request by source branch."""
        mock_project = Mock()
        mock_project.mergerequests.list.return_value = [mock_merge_request]  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        result = gitlab_client.get_merge_request_by_branch("123", "feature-branch")

        assert result == mock_merge_request

    def test_get_merge_request_by_branch_not_found(
        self, gitlab_client: GitLabClient
    ) -> None:
        """Test finding merge request by source branch when none exists."""
        mock_project = Mock()
        mock_project.mergerequests.list.return_value = []  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        result = gitlab_client.get_merge_request_by_branch("123", "feature-branch")

        assert result is None

    def test_get_pipeline_error_no_pipelines(
        self, gitlab_client: GitLabClient, mock_merge_request: Mock
    ) -> None:
        """Test getting pipeline error when no pipelines exist."""
        mock_merge_request.pipelines.list.return_value = []

        result = gitlab_client.get_pipeline_error(mock_merge_request)

        assert result is None

    def test_get_pipeline_error_success_pipeline(
        self, gitlab_client: GitLabClient, mock_merge_request: Mock
    ) -> None:
        """Test getting pipeline error when pipeline is successful."""
        mock_pipeline_ref = Mock()
        mock_pipeline_ref.id = "pipeline123"
        mock_merge_request.pipelines.list.return_value = [mock_pipeline_ref]

        mock_project = Mock()
        mock_pipeline = Mock()
        mock_pipeline.status = "success"
        mock_project.pipelines.get.return_value = mock_pipeline  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        result = gitlab_client.get_pipeline_error(mock_merge_request)

        assert result is None

    def test_get_pipeline_error_failed_pipeline(
        self, gitlab_client: GitLabClient, mock_merge_request: Mock
    ) -> None:
        """Test getting pipeline error when pipeline failed."""
        mock_pipeline_ref = Mock()
        mock_pipeline_ref.id = "pipeline123"
        mock_merge_request.pipelines.list.return_value = [mock_pipeline_ref]

        mock_project = Mock()
        mock_pipeline = Mock()
        mock_pipeline.status = "failed"

        # Mock failed job in the jobs list
        mock_job = Mock()
        mock_job.configure_mock(
            **{
                "id": "job123",
                "status": "failed",
                "name": "test-job",
                "ref": "feature-branch",
            }
        )
        mock_pipeline.jobs.list.return_value = [mock_job]

        # Mock the detailed job object returned by project.jobs.get()
        mock_job_detail = Mock()
        mock_job_detail.configure_mock(
            **{"id": "job123", "name": "test-job", "ref": "feature-branch"}
        )
        mock_job_detail.trace.return_value = (
            "Test error message\n=== FAILURES ===\nDetailed error info"
        )
        mock_project.jobs.get.return_value = mock_job_detail  # type: ignore

        mock_project.pipelines.get.return_value = mock_pipeline  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        result = gitlab_client.get_pipeline_error(mock_merge_request)

        assert result is not None
        assert 'Job #job123 "test-job" (feature-branch) failed:' in result
        assert "Detailed error info" in result

    def test_submit_review_changes_existing_mr(
        self, gitlab_client: GitLabClient, mock_merge_request: Mock
    ) -> None:
        """Test submitting review changes to existing MR."""
        gitlab_client.get_merge_request_by_branch = Mock(return_value=mock_merge_request)  # type: ignore

        result = gitlab_client.submit_review_changes(
            project_id="123",
            discussion_id="discussion123",
            commit_id="abc123",
            source_branch="feature-branch",
            title="Test Title",
            description="Test description",
        )

        assert result == mock_merge_request.web_url
        mock_merge_request.discussions.get.assert_called_once_with("discussion123")

    def test_submit_review_changes_new_mr(
        self, gitlab_client: GitLabClient, mock_user: Mock
    ) -> None:
        """Test submitting review changes creates new MR."""
        gitlab_client.get_merge_request_by_branch = Mock(return_value=None)  # type: ignore
        gitlab_client.get_user = Mock(return_value=mock_user)  # type: ignore

        mock_project = Mock()
        mock_new_mr = Mock()
        mock_new_mr.attributes = {
            "web_url": "https://gitlab.com/test/test-project/-/merge_requests/2"
        }
        mock_project.mergerequests.create.return_value = mock_new_mr  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        result = gitlab_client.submit_review_changes(
            project_id="123",
            discussion_id=None,
            commit_id="abc123",
            source_branch="feature-branch",
            title="Test Title",
            description="Test description",
        )

        assert result == "https://gitlab.com/test/test-project/-/merge_requests/2"
        mock_project.mergerequests.create.assert_called_once()
        create_args = mock_project.mergerequests.create.call_args[0][0]
        assert create_args["source_branch"] == "feature-branch"
        assert create_args["target_branch"] == "main"
        assert create_args["title"] == "Test Title"
        assert create_args["description"] == "Test description"
        assert create_args["assignee_id"] == 42

    @patch("neia.gitlab_client.click.echo")
    def test_process_merge_request_mr_not_found(
        self, mock_echo: Mock, gitlab_client: GitLabClient
    ) -> None:
        """Test processing MR when MR is not found."""
        gitlab_client.get_merge_request = Mock(return_value=None)  # type: ignore

        gitlab_client.process_merge_request(
            dry_run=False, prompt_only=False, mr_id="456"
        )

        mock_echo.assert_called_with("The MR was not found.")

    @patch("neia.gitlab_client.click.echo")
    def test_process_merge_request_mr_not_opened(
        self, mock_echo: Mock, gitlab_client: GitLabClient, mock_merge_request: Mock
    ) -> None:
        """Test processing MR when MR is not in opened state."""
        mock_merge_request.state = "closed"
        gitlab_client.get_merge_request = Mock(return_value=mock_merge_request)  # type: ignore

        gitlab_client.process_merge_request(
            dry_run=False, prompt_only=False, mr_id="456"
        )

        mock_echo.assert_called_with("The MR is not opened.")

    @patch("neia.issue_loader.get_issue_from_url")
    @patch("neia.repository_analyzer.analyze")
    def test_process_merge_request_success(
        self,
        mock_analyze: Mock,
        mock_get_issue: Mock,
        gitlab_client: GitLabClient,
        mock_merge_request: Mock,
    ) -> None:
        """Test successful MR processing."""
        mock_issue = Mock()
        mock_get_issue.return_value = mock_issue
        gitlab_client.get_merge_request = Mock(return_value=mock_merge_request)  # type: ignore

        gitlab_client.process_merge_request(
            dry_run=False, prompt_only=False, mr_id="456"
        )

        mock_get_issue.assert_called_once_with(
            mock_merge_request.description, mock_merge_request.project_id
        )
        mock_analyze.assert_called_once_with(
            gitlab_client,
            mock_merge_request.project_id,
            mock_merge_request.source_branch,
            mock_issue,
            mock_merge_request,
            False,
            False,
            False,
        )

    @patch("neia.issue_loader.get_issue_from_url")
    @patch("neia.gitlab_client.click.echo")
    def test_process_merge_request_issue_error(
        self,
        mock_echo: Mock,
        mock_get_issue: Mock,
        gitlab_client: GitLabClient,
        mock_merge_request: Mock,
    ) -> None:
        """Test MR processing when issue loading fails."""
        mock_get_issue.side_effect = Exception("Issue not found")
        gitlab_client.get_merge_request = Mock(return_value=mock_merge_request)  # type: ignore

        gitlab_client.process_merge_request(
            dry_run=False, prompt_only=False, mr_id="456"
        )

        mock_echo.assert_called_with("Issue not found")

    @patch("builtins.print")
    def test_print_merge_request_details_not_found(
        self, mock_print: Mock, gitlab_client: GitLabClient
    ) -> None:
        """Test printing MR details when MR is not found."""
        gitlab_client.get_merge_request = Mock(return_value=None)  # type: ignore

        with patch("neia.gitlab_client.Console") as mock_console_class:
            mock_console = Mock()
            mock_console_class.return_value = mock_console

            gitlab_client.print_merge_request_details("456")

            mock_console.print.assert_called_with("The MR was not found.")

    def test_print_merge_request_details_success(
        self, gitlab_client: GitLabClient, mock_merge_request: Mock
    ) -> None:
        """Test successful printing of MR details."""
        gitlab_client.get_merge_request = Mock(return_value=mock_merge_request)  # type: ignore

        mock_project = Mock()
        mock_project.name = "test-project"
        mock_project.path = "test/test-project"
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        with patch("neia.gitlab_client.Console") as mock_console_class:
            mock_console = Mock()
            mock_console_class.return_value = mock_console

            gitlab_client.print_merge_request_details("456")

            # Verify that console.print was called multiple times with MR details
            assert mock_console.print.call_count > 5
            print_calls = [call[0][0] for call in mock_console.print.call_args_list]
            assert any("ID: 456" in str(call) for call in print_calls)
            assert any("Test MR" in str(call) for call in print_calls)

    @patch("neia.gitlab_client.Console")
    @patch("neia.gitlab_client.inquirer")
    def test_print_merge_requests_stats_no_processing_times(
        self,
        mock_inquirer: Mock,
        mock_console_class: Mock,
        gitlab_client: GitLabClient,
        mock_user: Mock,
    ) -> None:
        """Test that division by zero doesn't occur when no processing times are available."""
        gitlab_client.gl.user = mock_user

        mock_console = Mock()
        mock_console_class.return_value = mock_console

        mock_mr_opened = Mock()
        mock_mr_opened.id = "123"
        mock_mr_opened.iid = "1"
        mock_mr_opened.title = "Opened MR"
        mock_mr_opened.state = "opened"
        mock_mr_opened.project_id = "456"
        mock_mr_opened.source_branch = "feature-branch"
        mock_mr_opened.created_at = "2023-01-01T00:00:00Z"
        mock_mr_opened.merged_at = None
        mock_mr_opened.closed_at = None

        mock_discussions = Mock()
        mock_discussions.list.return_value = []
        mock_mr_opened.discussions = mock_discussions

        gitlab_client.gl.mergerequests.list.return_value = [mock_mr_opened]  # type: ignore

        mock_project = Mock()
        mock_project.path = "test/project"
        mock_project.mergerequests.get.return_value = mock_mr_opened  # type: ignore
        gitlab_client.gl.projects.get.return_value = mock_project  # type: ignore

        gitlab_client.get_pipeline_error = Mock(return_value=None)  # type: ignore

        mock_inquirer.select.return_value.execute.return_value = "exit"

        gitlab_client.print_merge_requests_stats(export_format=None)

        assert mock_console.print.call_count > 0

        print_calls = [call[0][0] for call in mock_console.print.call_args_list]
        processing_time_calls = [
            call for call in print_calls if "Temps moyen de traitement" in str(call)
        ]
        assert len(processing_time_calls) == 0

    @patch("neia.gitlab_client.Console")
    def test_print_merge_requests_stats_no_mrs(
        self, mock_console_class: Mock, gitlab_client: GitLabClient, mock_user: Mock
    ) -> None:
        """Test that method returns early with appropriate message when user has no merge requests."""
        gitlab_client.gl.user = mock_user

        mock_console = Mock()
        mock_console_class.return_value = mock_console

        gitlab_client.gl.mergerequests.list.return_value = []  # type: ignore

        gitlab_client.print_merge_requests_stats(export_format=None)

        assert mock_console.print.call_count > 0

        print_calls = [call[0][0] for call in mock_console.print.call_args_list]
        assert any("NEIA Merge Requests Dashboard" in str(call) for call in print_calls)
        assert any(
            "Aucune merge request assignée trouvée" in str(call) for call in print_calls
        )
        assert any(
            "Vous n'avez actuellement aucune merge request assignée" in str(call)
            for call in print_calls
        )
