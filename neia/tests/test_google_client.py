from typing import Any, cast
from unittest.mock import Mock, mock_open, patch

import pytest
from google.auth.exceptions import RefreshError
from google.oauth2.credentials import Credentials

from neia.google_client import GoogleClient, generate_token
from neia.google_docs_deserializer import ContentElement
from neia.models import NeiaIssue


class TestGoogleClient:
    """Test cases for GoogleClient class."""

    @pytest.fixture
    def google_client(self) -> GoogleClient:
        """Create a GoogleClient instance for testing."""
        return GoogleClient(token_file="test_token.json")

    @pytest.fixture
    def mock_credentials(self) -> Mock:
        """Create mock credentials."""
        creds = Mock(spec=Credentials)
        creds.valid = True
        return creds

    @pytest.fixture
    def sample_google_doc_data(self) -> dict[str, Any]:
        """Sample Google Docs document data for testing."""
        return {
            "documentId": "test_doc_id_123",
            "title": "Test Document Title",
            "body": {
                "content": [
                    {
                        "paragraph": {
                            "elements": [
                                {"textRun": {"content": "This is test content."}}
                            ]
                        }
                    },
                    {
                        "paragraph": {
                            "elements": [
                                {"textRun": {"content": "Second paragraph content."}}
                            ]
                        }
                    },
                ]
            },
        }

    def test_init(self) -> None:
        """Test GoogleClient initialization."""
        client = GoogleClient("test_token.json")
        assert client.token_file == "test_token.json"
        assert client.credentials is None

    @patch("os.path.exists")
    @patch("neia.google_client.Credentials.from_authorized_user_file")
    def test_auth_success_with_valid_token(
        self,
        mock_from_file: Mock,
        mock_exists: Mock,
        google_client: GoogleClient,
        mock_credentials: Mock,
    ) -> None:
        """Test successful authentication with valid token file."""
        mock_exists.return_value = True
        mock_from_file.return_value = mock_credentials

        result = google_client.auth()

        assert result is True
        assert google_client.credentials == mock_credentials

    @patch("os.path.exists")
    def test_auth_failure_no_token_file(
        self, mock_exists: Mock, google_client: GoogleClient
    ) -> None:
        """Test authentication failure when token file doesn't exist."""
        mock_exists.return_value = False

        result = google_client.auth()

        assert result is False
        assert google_client.credentials is None

    @patch("os.path.exists")
    @patch("neia.google_client.Credentials.from_authorized_user_file")
    def test_auth_failure_invalid_credentials(
        self, mock_from_file: Mock, mock_exists: Mock, google_client: GoogleClient
    ) -> None:
        """Test authentication failure with invalid credentials."""
        mock_exists.return_value = True
        mock_creds = Mock(spec=Credentials)
        mock_creds.valid = False
        mock_from_file.return_value = mock_creds

        result = google_client.auth()

        assert result is False
        assert google_client.credentials is None

    @patch("os.path.exists")
    @patch("neia.google_client.Credentials.from_authorized_user_file")
    def test_auth_failure_none_credentials(
        self, mock_from_file: Mock, mock_exists: Mock, google_client: GoogleClient
    ) -> None:
        """Test authentication failure when credentials are None."""
        mock_exists.return_value = True
        mock_from_file.return_value = None

        result = google_client.auth()

        assert result is False
        assert google_client.credentials is None

    def test_parse_content_empty_list(self, google_client: GoogleClient) -> None:
        """Test parsing empty content list."""
        result = google_client.parse_content([])
        assert result == ""

    def test_parse_content_with_text(self, google_client: GoogleClient) -> None:
        """Test parsing content with text elements."""
        # Create mock content elements
        element1 = Mock(spec=ContentElement)
        element1.get_text_content.return_value = "First text"

        element2 = Mock(spec=ContentElement)
        element2.get_text_content.return_value = "Second text"

        content = cast(list[ContentElement], [element1, element2])

        result = google_client.parse_content(content)

        assert result == "First text Second text"

    def test_parse_content_with_empty_elements(
        self, google_client: GoogleClient
    ) -> None:
        """Test parsing content with empty elements."""
        element1 = Mock(spec=ContentElement)
        element1.get_text_content.return_value = ""

        element2 = Mock(spec=ContentElement)
        element2.get_text_content.return_value = None

        element3 = Mock(spec=ContentElement)
        element3.get_text_content.return_value = "Valid text"

        content = cast(list[ContentElement], [element1, element2, element3])

        result = google_client.parse_content(content)

        assert result == "Valid text"

    @patch("neia.google_client.build")
    @patch("neia.google_client.GoogleDocsDeserializer.deserialize_content")
    def test_get_issue_success(
        self,
        mock_deserialize: Mock,
        mock_build: Mock,
        google_client: GoogleClient,
        mock_credentials: Mock,
        sample_google_doc_data: dict[str, Any],
    ) -> None:
        """Test successful issue retrieval from Google Docs."""
        google_client.credentials = mock_credentials

        # Setup mocks for the call chain: build().documents().get().execute()
        mock_service = Mock()
        mock_documents = Mock()
        mock_get = Mock()

        mock_build.return_value = mock_service
        mock_service.documents.return_value = mock_documents
        mock_documents.get.return_value = mock_get
        mock_get.execute.return_value = sample_google_doc_data

        # Mock content element
        mock_element = Mock()
        mock_element.get_text_content.return_value = "Test document content"
        mock_deserialize.return_value = [mock_element]

        result = google_client.get_issue("test_doc_id")

        # Assertions
        assert isinstance(result, NeiaIssue)
        assert result.id == "test_doc_id_123"
        assert result.source == "gdocs"
        assert result.title == "Test Document Title"
        assert result.description == "Test document content"
        assert result.comments == []

    @patch("neia.google_client.build")
    def test_get_issue_missing_fields(
        self, mock_build: Mock, google_client: GoogleClient, mock_credentials: Mock
    ) -> None:
        """Test issue retrieval with missing document fields."""
        google_client.credentials = mock_credentials

        # Document data with missing fields
        incomplete_doc: dict[str, Any] = {"body": {"content": []}}

        mock_service = Mock()
        mock_documents = Mock()
        mock_get = Mock()

        mock_build.return_value = mock_service
        mock_service.documents.return_value = mock_documents
        mock_documents.get.return_value = mock_get
        mock_get.execute.return_value = incomplete_doc

        with patch(
            "neia.google_client.GoogleDocsDeserializer.deserialize_content"
        ) as mock_deserialize:
            mock_deserialize.return_value = []

            result = google_client.get_issue("test_doc_id")

            assert result.id == ""
            assert result.title == ""
            assert result.description == ""


class TestGenerateToken:
    """Test cases for generate_token function."""

    @pytest.fixture
    def mock_creds(self) -> Mock:
        """Create mock credentials."""
        creds = Mock(spec=Credentials)
        creds.valid = True
        creds.expired = False
        creds.refresh_token = "refresh_token"
        creds.to_json.return_value = '{"token": "test_token"}'
        return creds

    @patch("os.path.exists")
    @patch("neia.google_client.Credentials.from_authorized_user_file")
    @patch("builtins.print")
    def test_generate_token_valid_existing(
        self,
        mock_print: Mock,
        mock_from_file: Mock,
        mock_exists: Mock,
        mock_creds: Mock,
    ) -> None:
        """Test generate_token with valid existing credentials."""
        mock_exists.return_value = True
        mock_from_file.return_value = mock_creds

        generate_token("credentials.json", "token.json")

        mock_print.assert_called_once_with("Valid token already exists.")
        mock_from_file.assert_called_once_with(
            "token.json", ["https://www.googleapis.com/auth/documents.readonly"]
        )

    @patch("os.path.exists")
    @patch("neia.google_client.Credentials.from_authorized_user_file")
    @patch("neia.google_client.Request")
    @patch("builtins.open", new_callable=mock_open)
    @patch("builtins.print")
    def test_generate_token_refresh_success(
        self,
        mock_print: Mock,
        mock_file: Mock,
        mock_request: Mock,
        mock_from_file: Mock,
        mock_exists: Mock,
    ) -> None:
        """Test generate_token with successful credential refresh."""
        mock_exists.return_value = True

        mock_creds = Mock(spec=Credentials)
        mock_creds.valid = False
        mock_creds.expired = True
        mock_creds.refresh_token = "refresh_token"
        mock_creds.to_json.return_value = '{"token": "refreshed_token"}'

        mock_from_file.return_value = mock_creds

        generate_token("credentials.json", "token.json")

        mock_creds.refresh.assert_called_once()
        mock_file.assert_called_once_with("token.json", "w")
        mock_file().write.assert_called_once_with('{"token": "refreshed_token"}')
        mock_print.assert_called_once_with("The token was generated.")

    @patch("os.path.exists")
    @patch("neia.google_client.Credentials.from_authorized_user_file")
    @patch("neia.google_client.Request")
    @patch("builtins.open", new_callable=mock_open)
    @patch("builtins.print")
    def test_generate_token_refresh_error_device_flow(
        self,
        mock_print: Mock,
        mock_file: Mock,
        mock_request: Mock,
        mock_from_file: Mock,
        mock_exists: Mock,
    ) -> None:
        """Test generate_token with refresh error - simplified test."""
        mock_exists.return_value = True

        mock_creds = Mock(spec=Credentials)
        mock_creds.valid = False
        mock_creds.expired = True
        mock_creds.refresh_token = "refresh_token"
        mock_creds.refresh.side_effect = RefreshError("Refresh failed")

        mock_from_file.return_value = mock_creds

        # The device flow will fail when trying to parse the credentials file
        # since we haven't mocked it properly in this simplified test
        with pytest.raises((FileNotFoundError, OSError, ValueError)):
            generate_token("credentials.json", "token.json")

    @patch("os.path.exists")
    @patch("builtins.input")
    @patch("builtins.open", new_callable=mock_open)
    @patch("builtins.print")
    @patch("neia.google_client.InstalledAppFlow")
    def test_generate_token_no_existing_file(
        self,
        mock_flow_class: Mock,
        mock_print: Mock,
        mock_file: Mock,
        mock_input: Mock,
        mock_exists: Mock,
    ) -> None:
        """Test generate_token when no token file exists."""
        mock_exists.return_value = False
        mock_input.return_value = "test_auth_code"

        # Mock the flow instance
        mock_flow = Mock()
        mock_flow.authorization_url.return_value = ("https://test.com/auth", None)
        mock_flow.credentials.to_json.return_value = '{"token": "new_token"}'
        mock_flow_class.from_client_secrets_file.return_value = mock_flow

        generate_token("credentials.json", "token.json")

        # Verify the flow was created with correct parameters
        mock_flow_class.from_client_secrets_file.assert_called_once_with(
            "credentials.json",
            ["https://www.googleapis.com/auth/documents.readonly"],
            redirect_uri="urn:ietf:wg:oauth:2.0:oob",
        )

        # Verify authorization URL was generated
        mock_flow.authorization_url.assert_called_once_with(prompt="consent")

        # Verify user was prompted for auth code
        mock_input.assert_called_once()

        # Verify token was fetched
        mock_flow.fetch_token.assert_called_once_with(code="test_auth_code")

        # Verify token was saved
        mock_file.assert_called_with("token.json", "w")
        mock_file().write.assert_called_once_with('{"token": "new_token"}')

        # Verify success message
        assert any(
            "The token was generated." in str(call)
            for call in mock_print.call_args_list
        )

    @patch("os.path.exists")
    @patch("builtins.input")
    @patch("builtins.open", new_callable=mock_open)
    @patch("builtins.print")
    @patch("neia.google_client.InstalledAppFlow")
    def test_generate_token_device_flow_user_denied(
        self,
        mock_flow_class: Mock,
        mock_print: Mock,
        mock_file: Mock,
        mock_input: Mock,
        mock_exists: Mock,
    ) -> None:
        """Test generate_token when user denies access in OAuth flow."""
        mock_exists.return_value = False
        mock_input.return_value = "invalid_auth_code"

        # Mock the flow instance to raise an exception
        mock_flow = Mock()
        mock_flow.authorization_url.return_value = ("https://test.com/auth", None)
        mock_flow.fetch_token.side_effect = Exception("Invalid authorization code")
        mock_flow_class.from_client_secrets_file.return_value = mock_flow

        with pytest.raises(Exception, match="Invalid authorization code"):
            generate_token("credentials.json", "token.json")

    @patch("os.path.exists")
    @patch("builtins.input")
    @patch("builtins.open", new_callable=mock_open)
    @patch("builtins.print")
    @patch("neia.google_client.InstalledAppFlow")
    def test_generate_token_device_flow_expired_token(
        self,
        mock_flow_class: Mock,
        mock_print: Mock,
        mock_file: Mock,
        mock_input: Mock,
        mock_exists: Mock,
    ) -> None:
        """Test generate_token when auth code expires."""
        mock_exists.return_value = False
        mock_input.return_value = "expired_auth_code"

        # Mock the flow instance to raise an exception for expired code
        mock_flow = Mock()
        mock_flow.authorization_url.return_value = ("https://test.com/auth", None)
        mock_flow.fetch_token.side_effect = Exception("Authorization code expired")
        mock_flow_class.from_client_secrets_file.return_value = mock_flow

        with pytest.raises(Exception, match="Authorization code expired"):
            generate_token("credentials.json", "token.json")

    @patch("os.path.exists")
    @patch("builtins.input")
    @patch("builtins.open", new_callable=mock_open)
    @patch("builtins.print")
    @patch("neia.google_client.InstalledAppFlow")
    def test_generate_token_device_flow_timeout(
        self,
        mock_flow_class: Mock,
        mock_print: Mock,
        mock_file: Mock,
        mock_input: Mock,
        mock_exists: Mock,
    ) -> None:
        """Test generate_token when OAuth flow times out."""
        mock_exists.return_value = False
        mock_input.return_value = "timeout_auth_code"

        # Mock the flow instance to raise a timeout exception
        mock_flow = Mock()
        mock_flow.authorization_url.return_value = ("https://test.com/auth", None)
        mock_flow.fetch_token.side_effect = Exception("Request timeout")
        mock_flow_class.from_client_secrets_file.return_value = mock_flow

        with pytest.raises(Exception, match="Request timeout"):
            generate_token("credentials.json", "token.json")

    @patch("os.path.exists")
    @patch("builtins.input")
    @patch("builtins.open", new_callable=mock_open)
    @patch("builtins.print")
    @patch("neia.google_client.InstalledAppFlow")
    def test_generate_token_device_code_request_failure(
        self,
        mock_flow_class: Mock,
        mock_print: Mock,
        mock_file: Mock,
        mock_input: Mock,
        mock_exists: Mock,
    ) -> None:
        """Test generate_token when OAuth flow initialization fails."""
        mock_exists.return_value = False

        # Mock the flow class to raise an exception during initialization
        mock_flow_class.from_client_secrets_file.side_effect = Exception(
            "Invalid credentials file"
        )

        with pytest.raises(Exception, match="Invalid credentials file"):
            generate_token("credentials.json", "token.json")
