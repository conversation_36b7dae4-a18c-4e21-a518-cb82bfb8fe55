from unittest.mock import Mock, patch

import pytest
from git import <PERSON>o

from neia.aider_manager import AiderCoder<PERSON>onfig, Aider<PERSON>anager, ManagedCoder


class TestAiderCoderConfig:
    """Test the AiderCoderConfig class."""

    def test_default_config(self) -> None:
        """Test default configuration values."""
        config = AiderCoderConfig()

        assert config.model_name == "gpt-4o-mini"
        assert config.edit_format is None
        assert config.detect_urls is False
        assert config.attribute_author is False
        assert config.attribute_committer is False
        assert config.yes_always is True
        assert config.conventions_file_path is None

    def test_custom_config(self) -> None:
        """Test custom configuration values."""
        config = AiderCoderConfig(
            model_name="gpt-4",
            edit_format="ask",
            detect_urls=True,
            attribute_author=True,
            attribute_committer=True,
            yes_always=False,
            conventions_file_path="/custom/path/conventions.md",
        )

        assert config.model_name == "gpt-4"
        assert config.edit_format == "ask"
        assert config.detect_urls is True
        assert config.attribute_author is True
        assert config.attribute_committer is True
        assert config.yes_always is False
        assert config.conventions_file_path == "/custom/path/conventions.md"


class TestManagedCoder:
    """Test the ManagedCoder class."""

    @pytest.fixture
    def mock_coder(self) -> Mock:
        """Create a mock aider coder."""
        return Mock()

    @pytest.fixture
    def managed_coder(self, mock_coder: Mock) -> ManagedCoder:
        """Create a ManagedCoder with a mock coder."""
        return ManagedCoder(mock_coder)

    def test_add_prompt(self, managed_coder: ManagedCoder) -> None:
        """Test adding prompts to the queue."""
        assert managed_coder.pending_prompts_count == 0

        managed_coder.add_prompt("First prompt")
        assert managed_coder.pending_prompts_count == 1

        managed_coder.add_prompt("Second prompt")
        assert managed_coder.pending_prompts_count == 2

    def test_clear_prompts(self, managed_coder: ManagedCoder) -> None:
        """Test clearing the prompt queue."""
        managed_coder.add_prompt("Test prompt")
        assert managed_coder.pending_prompts_count == 1

        managed_coder.clear_prompts()
        assert managed_coder.pending_prompts_count == 0

    def test_run_single_prompt(
        self, managed_coder: ManagedCoder, mock_coder: Mock
    ) -> None:
        """Test running a single prompt."""
        mock_coder.run.return_value = "Test response"

        managed_coder.add_prompt("Test prompt")
        result = managed_coder.run()

        assert result == "Test response"
        assert managed_coder.pending_prompts_count == 0
        mock_coder.run.assert_called_once_with("Test prompt")

    def test_run_multiple_prompts(
        self, managed_coder: ManagedCoder, mock_coder: Mock
    ) -> None:
        """Test running multiple prompts combined."""
        mock_coder.run.return_value = "Combined response"

        managed_coder.add_prompt("First prompt")
        managed_coder.add_prompt("Second prompt")
        result = managed_coder.run()

        assert result == "Combined response"
        assert managed_coder.pending_prompts_count == 0
        mock_coder.run.assert_called_once_with("First prompt\n\nSecond prompt")

    def test_run_no_prompts(
        self, managed_coder: ManagedCoder, mock_coder: Mock
    ) -> None:
        """Test running with no prompts in queue."""
        result = managed_coder.run()

        assert result == ""
        mock_coder.run.assert_not_called()

    def test_run_single_immediate(
        self, managed_coder: ManagedCoder, mock_coder: Mock
    ) -> None:
        """Test running a single prompt immediately without queuing."""
        mock_coder.run.return_value = "Immediate response"

        result = managed_coder.run_single("Immediate prompt")

        assert result == "Immediate response"
        mock_coder.run.assert_called_once_with("Immediate prompt")

    def test_run_with_exception(
        self, managed_coder: ManagedCoder, mock_coder: Mock
    ) -> None:
        """Test handling exceptions during prompt execution."""
        mock_coder.run.side_effect = Exception("Test error")

        managed_coder.add_prompt("Test prompt")

        with pytest.raises(Exception, match="Test error"):
            managed_coder.run()

    def test_pending_prompts_count(self, managed_coder: ManagedCoder) -> None:
        """Test that pending prompts count works correctly."""
        assert managed_coder.pending_prompts_count == 0

        managed_coder.add_prompt("Test prompt")
        assert managed_coder.pending_prompts_count == 1

    def test_technology_level_disabled_by_default(
        self, managed_coder: ManagedCoder
    ) -> None:
        """Test that technology level is disabled by default."""
        assert managed_coder.technology_level_enabled is False

    @patch("neia.aider_manager.dotenv_values")
    def test_enable_technology_level_with_levels(
        self, mock_dotenv_values: Mock, managed_coder: ManagedCoder
    ) -> None:
        """Test enabling technology level with valid levels."""
        mock_dotenv_values.return_value = {
            "TECHNOLOGY_LEVEL_PYTHON": "3",
            "TECHNOLOGY_LEVEL_JAVASCRIPT": "5",
            "OTHER_VAR": "value",
            "TECHNOLOGY_LEVEL_INVALID": "10",
        }

        managed_coder.enable_technology_level()

        assert managed_coder.technology_level_enabled is True

    @patch("neia.aider_manager.dotenv_values")
    def test_enable_technology_level_no_levels(
        self, mock_dotenv_values: Mock, managed_coder: ManagedCoder
    ) -> None:
        """Test enabling technology level with no valid levels."""
        mock_dotenv_values.return_value = {
            "OTHER_VAR": "value",
            "TECHNOLOGY_LEVEL_INVALID": "10",
        }

        managed_coder.enable_technology_level()

        assert managed_coder.technology_level_enabled is False

    @patch("neia.aider_manager.dotenv_values")
    def test_technology_level_prompt_enhancement(
        self, mock_dotenv_values: Mock, managed_coder: ManagedCoder, mock_coder: Mock
    ) -> None:
        """Test that prompts are enhanced with technology level context."""
        mock_dotenv_values.return_value = {
            "TECHNOLOGY_LEVEL_PYTHON": "2",
            "TECHNOLOGY_LEVEL_REACT": "4",
        }
        mock_coder.run.return_value = "Enhanced response"

        managed_coder.enable_technology_level()
        result = managed_coder.run_single("Test prompt")

        assert result == "Enhanced response"

        call_args = mock_coder.run.call_args[0][0]
        assert "technology levels" in call_args.lower()
        assert "PYTHON" in call_args
        assert "REACT" in call_args
        assert "Test prompt" in call_args

    @patch("neia.aider_manager.dotenv_values")
    def test_technology_level_prompt_enhancement_disabled(
        self, mock_dotenv_values: Mock, managed_coder: ManagedCoder, mock_coder: Mock
    ) -> None:
        """Test that prompts are not enhanced when technology level is disabled."""
        mock_dotenv_values.return_value = {}
        mock_coder.run.return_value = "Normal response"

        managed_coder.enable_technology_level()
        result = managed_coder.run_single("Test prompt")

        assert result == "Normal response"
        mock_coder.run.assert_called_once_with("Test prompt")

    @patch("neia.aider_manager.dotenv_values")
    def test_technology_level_with_run_method(
        self, mock_dotenv_values: Mock, managed_coder: ManagedCoder, mock_coder: Mock
    ) -> None:
        """Test that technology level works with the run method (queued prompts)."""
        mock_dotenv_values.return_value = {
            "TECHNOLOGY_LEVEL_PYTHON": "1",
        }
        mock_coder.run.return_value = "Queued response"

        managed_coder.enable_technology_level()
        managed_coder.add_prompt("First prompt")
        managed_coder.add_prompt("Second prompt")
        result = managed_coder.run()

        assert result == "Queued response"

        call_args = mock_coder.run.call_args[0][0]
        assert "technology levels" in call_args.lower()
        assert "First prompt\n\nSecond prompt" in call_args

    @patch("neia.aider_manager.dotenv_values")
    def test_technology_level_filtering_invalid_values(
        self, mock_dotenv_values: Mock, managed_coder: ManagedCoder
    ) -> None:
        """Test that only valid technology level values are processed."""
        mock_dotenv_values.return_value = {
            "TECHNOLOGY_LEVEL_VALID1": "3",
            "TECHNOLOGY_LEVEL_VALID2": "5",
            "TECHNOLOGY_LEVEL_TOO_HIGH": "6",
            "TECHNOLOGY_LEVEL_TOO_LOW": "0",
            "TECHNOLOGY_LEVEL_NOT_NUMBER": "abc",
            "TECHNOLOGY_LEVEL_NONE": None,
            "NOT_TECH_LEVEL": "3",
        }

        managed_coder.enable_technology_level()

        assert managed_coder.technology_level_enabled is True

    @patch("neia.aider_manager.dotenv_values")
    def test_technology_level_boundary_values(
        self, mock_dotenv_values: Mock, managed_coder: ManagedCoder
    ) -> None:
        """Test that boundary values (1 and 5) are accepted."""
        mock_dotenv_values.return_value = {
            "TECHNOLOGY_LEVEL_MIN": "1",
            "TECHNOLOGY_LEVEL_MAX": "5",
        }

        managed_coder.enable_technology_level()

        assert managed_coder.technology_level_enabled is True


class TestAiderManager:
    """Test the AiderManager class."""

    @pytest.fixture
    def mock_repo_manager(self) -> Mock:
        """Create a mock repository manager."""
        return Mock()

    @pytest.fixture
    def aider_manager(self, mock_repo_manager: Mock) -> AiderManager:
        """Create an AiderManager with a mock repository manager."""
        return AiderManager(mock_repo_manager)

    @patch("neia.aider_manager.Coder")
    @patch("neia.aider_manager.GitRepo")
    @patch("neia.aider_manager.InputOutput")
    @patch("neia.aider_manager.Model")
    @patch("neia.aider_manager.Path")
    def test_create_coder_default_config(
        self,
        mock_path: Mock,
        mock_model: Mock,
        mock_io: Mock,
        mock_git_repo: Mock,
        mock_coder_class: Mock,
    ) -> None:
        """Test creating a coder with default configuration."""
        mock_path.return_value.exists.return_value = True
        mock_coder_instance = Mock()
        mock_coder_class.create.return_value = mock_coder_instance

        result = AiderManager.create_coder("/test/repo")

        assert isinstance(result, ManagedCoder)
        mock_coder_class.create.assert_called_once()

    @patch("neia.aider_manager.Coder")
    @patch("neia.aider_manager.GitRepo")
    @patch("neia.aider_manager.InputOutput")
    @patch("neia.aider_manager.Model")
    @patch("neia.aider_manager.Path")
    def test_create_ask_coder(
        self,
        mock_path: Mock,
        mock_model: Mock,
        mock_io: Mock,
        mock_git_repo: Mock,
        mock_coder_class: Mock,
    ) -> None:
        """Test creating a coder in ask mode."""
        mock_path.return_value.exists.return_value = False
        mock_coder_instance = Mock()
        mock_coder_class.create.return_value = mock_coder_instance

        result = AiderManager.create_ask_coder("/test/repo")

        assert isinstance(result, ManagedCoder)
        mock_coder_class.create.assert_called_once()

    def test_config_properties(self) -> None:
        """Test that configuration properties are properly set."""
        config = AiderCoderConfig(
            model_name="test-model",
            edit_format="test-format",
            detect_urls=True,
            conventions_file_path="/test/path",
        )

        assert config.model_name == "test-model"
        assert config.edit_format == "test-format"
        assert config.detect_urls is True
        assert config.conventions_file_path == "/test/path"

    def test_create_coder_for_git_repo(self, aider_manager: AiderManager) -> None:
        """Test creating a coder for an existing git repository."""
        mock_repo = Mock(spec=Repo)
        mock_repo.working_dir = "/test/repo"

        with patch.object(AiderManager, "create_coder") as mock_create:
            mock_managed_coder = Mock(spec=ManagedCoder)
            mock_create.return_value = mock_managed_coder

            result = aider_manager.create_coder_for_git_repo(mock_repo)

            assert result == mock_managed_coder
            mock_create.assert_called_once_with("/test/repo", None)

    def test_create_coder_for_git_repo_no_working_dir(
        self, aider_manager: AiderManager
    ) -> None:
        """Test creating a coder for a git repository without working directory."""
        mock_repo = Mock(spec=Repo)
        mock_repo.working_dir = None

        with pytest.raises(
            ValueError, match="Repository must have a working directory"
        ):
            aider_manager.create_coder_for_git_repo(mock_repo)

    def test_create_ask_coder_for_git_repo(self, aider_manager: AiderManager) -> None:
        """Test creating an ask coder for an existing git repository."""
        mock_repo = Mock(spec=Repo)
        mock_repo.working_dir = "/test/repo"

        with patch.object(AiderManager, "create_ask_coder") as mock_create:
            mock_managed_coder = Mock(spec=ManagedCoder)
            mock_create.return_value = mock_managed_coder

            result = aider_manager.create_ask_coder_for_git_repo(mock_repo)

            assert result == mock_managed_coder
            mock_create.assert_called_once_with("/test/repo", None)
