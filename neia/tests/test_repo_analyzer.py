from pathlib import Path
from unittest.mock import Mock, patch

import git
import pytest
from gitlab.v4.objects import CurrentUser

from neia.gitlab_client import GitLabClient
from neia.models import IssueComment, NeiaIssue
from neia.repository_analyzer import <PERSON>R<PERSON><PERSON>, RepositoryAnalyzer, analyze


class TestRepositoryAnalyzer:
    """Test cases for RepositoryAnalyzer class."""

    @pytest.fixture
    def temp_base_path(self, tmp_path: Path) -> Path:
        """Create a temporary base path for testing."""
        return tmp_path / "test_projects"

    @pytest.fixture
    def analyzer(self, temp_base_path: Path) -> RepositoryAnalyzer:
        """Create a RepositoryAnalyzer instance for testing."""
        return RepositoryAnalyzer(
            project_name="test-project",
            git_url="**************:test/test-project.git",
            model_name="gpt-4o-mini",
            base_path=str(temp_base_path),
        )

    @pytest.fixture
    def mock_repo(self) -> Mock:
        """Create a mock git repository."""
        repo = Mock(spec=git.Repo)
        repo.working_dir = "/tmp/test-project"
        repo.active_branch.name = "main"

        # Mock head commit
        mock_commit = Mock()
        mock_commit.hexsha = "abcdef123456"
        mock_commit.message = "Test commit message"
        repo.head.commit = mock_commit

        # Mock remotes
        mock_origin = Mock()
        repo.remotes.origin = mock_origin

        # Mock config writer
        mock_config = Mock()
        repo.config_writer.return_value = mock_config
        mock_config.set_value.return_value = mock_config
        mock_config.release.return_value = None

        return repo

    @pytest.fixture
    def mock_issue(self) -> NeiaIssue:
        """Create a mock NeiaIssue for testing."""
        return NeiaIssue(
            id="123",
            source="gitlab",
            title="Test Issue",
            description="Test issue description",
            comments=[
                IssueComment(id=1, author="User 1", body="First comment"),
                IssueComment(id=2, author="User 2", body="Second comment"),
            ],
            url="https://gitlab.com/test/test-project/-/issues/123",
        )

    @pytest.fixture
    def mock_user(self) -> Mock:
        """Create a mock CurrentUser."""
        user = Mock(spec=CurrentUser)
        user.attributes = {"id": 42, "name": "Test User"}
        return user

    def test_init(self, temp_base_path: Path) -> None:
        """Test RepositoryAnalyzer initialization."""
        analyzer = RepositoryAnalyzer(
            project_name="test-project",
            git_url="***************:test/repo.git",
            model_name="gpt-4",
            base_path=str(temp_base_path),
        )

        assert analyzer.project_name == "test-project"
        assert analyzer.git_url == "***************:test/repo.git"
        assert analyzer.model_name == "gpt-4"
        assert analyzer.repo_manager.base_path == temp_base_path
        assert temp_base_path.exists()

    def test_get_repo_path(
        self, analyzer: RepositoryAnalyzer, temp_base_path: Path
    ) -> None:
        """Test getting repository path."""
        expected_path = temp_base_path / "test-project"
        result = analyzer.repo_manager.get_repo_path("test-project")

        assert result == expected_path

    @patch("neia.repository_analyzer.git.Repo")
    def test_get_repo(
        self, mock_repo_class: Mock, analyzer: RepositoryAnalyzer
    ) -> None:
        """Test getting repository instance."""
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo

        result = analyzer.get_repo()

        mock_repo_class.assert_called_once()
        assert result == mock_repo

    @patch("neia.local_repository_manager.git.Repo")
    def test_ensure_local_repository_clone_new(
        self, mock_repo_class: Mock, analyzer: RepositoryAnalyzer, temp_base_path: Path
    ) -> None:
        """Test ensuring local repository when it doesn't exist (clone)."""
        mock_repo = Mock()
        mock_repo_class.clone_from.return_value = mock_repo

        result = analyzer.ensure_local_repository()

        mock_repo_class.clone_from.assert_called_once_with(
            "**************:test/test-project.git",
            str(analyzer.repo_manager.get_repo_path("test-project")),
        )
        assert result == mock_repo

    @patch("neia.local_repository_manager.git.Repo")
    def test_ensure_local_repository_update_existing(
        self, mock_repo_class: Mock, analyzer: RepositoryAnalyzer, temp_base_path: Path
    ) -> None:
        """Test ensuring local repository when it exists (update)."""
        # Create the directory to simulate existing repo
        repo_path = analyzer.repo_manager.get_repo_path("test-project")
        repo_path.mkdir(parents=True, exist_ok=True)

        mock_repo = Mock()
        mock_origin = Mock()
        mock_repo.remotes.origin = mock_origin
        mock_repo.active_branch.name = "main"

        # Mock the refs properly - the code checks [ref.name for ref in origin.refs]
        mock_ref = Mock()
        mock_ref.name = "origin/main"
        mock_origin.refs = [mock_ref]

        mock_repo_class.return_value = mock_repo

        result = analyzer.ensure_local_repository()

        mock_repo_class.assert_called_once_with(str(repo_path))
        mock_origin.fetch.assert_called_once()
        mock_origin.pull.assert_called_once()
        assert result == mock_repo

    @patch.object(RepositoryAnalyzer, "get_repo")
    def test_ensure_branch_new_from_main(
        self, mock_get_repo: Mock, analyzer: RepositoryAnalyzer
    ) -> None:
        """Test ensuring branch that doesn't exist (create from main)."""
        mock_repo = Mock()
        mock_repo.git.branch.return_value = ""  # No remote branches
        mock_repo.heads = []  # No local branches
        mock_get_repo.return_value = mock_repo

        analyzer.ensure_branch("feature-branch")

        mock_repo.git.checkout.assert_any_call("main")
        mock_repo.git.pull.assert_called_with("origin", "main")

    @patch.object(RepositoryAnalyzer, "get_repo")
    def test_ensure_branch_existing_remote(
        self, mock_get_repo: Mock, analyzer: RepositoryAnalyzer
    ) -> None:
        """Test ensuring branch that exists remotely."""
        mock_repo = Mock()
        mock_repo.git.branch.return_value = "origin/feature-branch"
        mock_repo.heads = []  # No local branch
        mock_get_repo.return_value = mock_repo

        analyzer.ensure_branch("feature-branch")

        mock_repo.git.checkout.assert_called_with(
            "-b", "feature-branch", "origin/feature-branch"
        )
        mock_repo.git.reset.assert_called_with("--hard", "origin/feature-branch")

    def test_generate_prompt_full_context(
        self, analyzer: RepositoryAnalyzer, mock_repo: Mock, mock_issue: NeiaIssue
    ) -> None:
        """Test generating prompt with full context."""
        mock_repo.git.log.return_value = "abc123 Test commit\nAuthor: Test User\nDate: 2023-01-01\n\nCommit message\n\nModified files:\nfile1.py\nfile2.py"

        result = analyzer.generate_prompt(
            mock_repo, "feature-branch", mock_issue, "MR description", "MR discussion"
        )

        assert "Issue title: Test Issue" in result
        assert "Issue description: Test issue description" in result
        assert "User 1: First comment" in result
        assert "User 2: Second comment" in result
        assert "Previous changes made for this issue:" in result
        assert "Original merge request made for this issue:" in result
        assert "MR description" in result
        assert "Discussion for this merge request:" in result
        assert "MR discussion" in result
        assert "Branch: feature-branch" in result

    def test_generate_prompt_minimal_context(
        self, analyzer: RepositoryAnalyzer, mock_repo: Mock
    ) -> None:
        """Test generating prompt with minimal context."""
        mock_repo.git.log.return_value = ""

        result = analyzer.generate_prompt(mock_repo, "feature-branch", None)

        assert "Current status:" in result
        assert "Branch: feature-branch" in result
        assert "Issue title:" not in result
        assert "Issue description:" not in result

    @patch("neia.repository_analyzer.AiderManager.create_coder")
    def test_get_coder(
        self,
        mock_create_coder: Mock,
        analyzer: RepositoryAnalyzer,
        mock_repo: Mock,
    ) -> None:
        """Test creating coder instance."""
        mock_coder = Mock()
        mock_create_coder.return_value = mock_coder

        result = analyzer.get_coder(mock_repo)

        assert result == mock_coder
        mock_create_coder.assert_called_once()

    @patch("neia.repository_analyzer.AiderManager.create_coder")
    def test_get_coder_with_edit_format(
        self,
        mock_create_coder: Mock,
        analyzer: RepositoryAnalyzer,
        mock_repo: Mock,
    ) -> None:
        """Test creating coder instance with specific edit format."""
        mock_coder = Mock()
        mock_create_coder.return_value = mock_coder

        result = analyzer.get_coder(mock_repo, "ask")

        assert result == mock_coder
        mock_create_coder.assert_called_once()
        call_args = mock_create_coder.call_args
        assert call_args is not None

    @patch.object(RepositoryAnalyzer, "get_coder")
    def test_get_result_prompt_only(
        self, mock_get_coder: Mock, analyzer: RepositoryAnalyzer, mock_repo: Mock
    ) -> None:
        """Test get_result with prompt_only=True."""
        mock_coder = Mock()
        mock_get_coder.return_value = mock_coder

        mock_coder.run_single.side_effect = ["5", "YES"]

        result = analyzer.get_result(
            mock_repo, "Test prompt", "discussion123", False, prompt_only=True
        )

        assert isinstance(result, AnalysisResult)
        assert result.coder_response is None
        expected_prompt = "Test prompt\nWhat changes should be made next to complete the task (without impacting the rest of the code)? You must begin your response with '*[NEIA]*'."
        assert result.error == expected_prompt
        assert result.discussion_id == "discussion123"

    @patch.object(RepositoryAnalyzer, "get_coder")
    def test_get_result_fix_errors(
        self, mock_get_coder: Mock, analyzer: RepositoryAnalyzer, mock_repo: Mock
    ) -> None:
        """Test get_result with fix_errors=True."""
        mock_coder = Mock()
        mock_ask_coder = Mock()
        mock_get_coder.side_effect = [mock_coder, mock_ask_coder]

        mock_ask_coder.run_single.side_effect = ["5", "YES", "Error fixed in French"]
        mock_coder.run_single.return_value = "Code changes applied"

        result = analyzer.get_result(
            mock_repo,
            "Pipeline error prompt",
            "discussion123",
            fix_errors=True,
            prompt_only=False,
        )

        assert result.coder_response == "Error fixed in French"
        assert result.commit_id == "abcdef1"  # First 7 chars of mock commit
        assert result.discussion_id == "discussion123"

    @patch.object(RepositoryAnalyzer, "get_coder")
    def test_get_result_normal_flow(
        self, mock_get_coder: Mock, analyzer: RepositoryAnalyzer, mock_repo: Mock
    ) -> None:
        """Test get_result with normal flow (not fix_errors)."""
        mock_coder = Mock()
        mock_ask_coder = Mock()
        mock_get_coder.side_effect = [mock_coder, mock_ask_coder]

        mock_ask_coder.run_single.side_effect = ["5", "YES", "French response"]
        mock_coder.run_single.return_value = "Code changes"

        result = analyzer.get_result(
            mock_repo, "Normal prompt", None, fix_errors=False, prompt_only=False
        )

        assert result.coder_response == "French response"
        assert result.commit_id == "abcdef1"
        assert result.discussion_id is None

    @patch.object(RepositoryAnalyzer, "get_coder")
    def test_get_result_exception(
        self, mock_get_coder: Mock, analyzer: RepositoryAnalyzer, mock_repo: Mock
    ) -> None:
        """Test get_result when exception occurs."""
        mock_get_coder.side_effect = Exception("Coder failed")

        result = analyzer.get_result(mock_repo, "Test prompt", None, False, False)

        assert result.coder_response is None
        assert result.error == "Coder failed"

    @patch.object(RepositoryAnalyzer, "ensure_local_repository")
    @patch.object(RepositoryAnalyzer, "ensure_branch")
    @patch.object(RepositoryAnalyzer, "generate_prompt")
    @patch.object(RepositoryAnalyzer, "get_result")
    def test_analyze_new_mr(
        self,
        mock_get_result: Mock,
        mock_generate_prompt: Mock,
        mock_ensure_branch: Mock,
        mock_ensure_local_repo: Mock,
        analyzer: RepositoryAnalyzer,
        mock_repo: Mock,
        mock_issue: NeiaIssue,
        mock_user: Mock,
    ) -> None:
        """Test analyze method when creating new MR."""
        mock_ensure_local_repo.return_value = mock_repo
        mock_generate_prompt.return_value = "Generated prompt"

        title_result = AnalysisResult(coder_response="MR Description", title="MR Title")
        main_result = AnalysisResult(coder_response="Main response", commit_id="abc123")
        mock_get_result.side_effect = [title_result, main_result]

        with patch.object(analyzer, "get_coder") as mock_get_coder:
            mock_ask_coder = Mock()
            mock_ask_coder.run_single.return_value = (
                "<title>Test Title</title><description>Test Description</description>"
            )
            mock_get_coder.return_value = mock_ask_coder

            results = analyzer.analyze(
                mock_issue,
                None,  # No MR (new)
                "feature-branch",
                None,  # No pipeline error
                mock_user,
                prompt_only=False,
            )

        assert len(results) == 2
        assert results[0].title == "Test Title"
        assert results[0].coder_response == "Test Description"

    @patch.object(RepositoryAnalyzer, "ensure_local_repository")
    @patch.object(RepositoryAnalyzer, "ensure_branch")
    @patch.object(RepositoryAnalyzer, "get_result")
    def test_analyze_with_pipeline_error(
        self,
        mock_get_result: Mock,
        mock_ensure_branch: Mock,
        mock_ensure_local_repo: Mock,
        analyzer: RepositoryAnalyzer,
        mock_repo: Mock,
        mock_issue: NeiaIssue,
        mock_user: Mock,
    ) -> None:
        """Test analyze method with pipeline error."""
        mock_ensure_local_repo.return_value = mock_repo
        mock_mr = Mock()

        pipeline_error = "Pipeline failed with error"

        error_result = AnalysisResult(coder_response="Error fixed", commit_id="def456")
        mock_get_result.return_value = error_result

        results = analyzer.analyze(
            mock_issue,
            mock_mr,
            "feature-branch",
            pipeline_error,
            mock_user,
            prompt_only=False,
        )

        assert len(results) == 1
        assert results[0].coder_response == "Error fixed"
        # Verify the prompt contained pipeline error info
        call_args = mock_get_result.call_args[0]
        assert "Pipeline failed with error" in call_args[1]

    @patch.object(RepositoryAnalyzer, "ensure_local_repository")
    @patch.object(RepositoryAnalyzer, "ensure_branch")
    @patch.object(RepositoryAnalyzer, "generate_prompt")
    @patch.object(RepositoryAnalyzer, "get_result")
    @patch("neia.repository_analyzer.resume_mr_discussion")
    def test_analyze_with_mr_discussions(
        self,
        mock_resume_discussion: Mock,
        mock_get_result: Mock,
        mock_generate_prompt: Mock,
        mock_ensure_branch: Mock,
        mock_ensure_local_repo: Mock,
        analyzer: RepositoryAnalyzer,
        mock_repo: Mock,
        mock_issue: NeiaIssue,
        mock_user: Mock,
    ) -> None:
        """Test analyze method with MR discussions."""
        mock_ensure_local_repo.return_value = mock_repo
        mock_generate_prompt.return_value = "Generated prompt"

        # Mock MR with discussions
        mock_mr = Mock()
        mock_discussion1 = Mock()
        mock_discussion1.id = "disc1"
        mock_discussion2 = Mock()
        mock_discussion2.id = "disc2"
        mock_mr.discussions.list.return_value = [mock_discussion1, mock_discussion2]

        # Mock resume_mr_discussion to return content for first discussion only
        mock_resume_discussion.side_effect = ["Discussion 1 content", None]

        # Mock result for the one valid discussion
        discussion_result = AnalysisResult(
            coder_response="Discussion response", discussion_id="disc1"
        )
        mock_get_result.return_value = discussion_result

        results = analyzer.analyze(
            mock_issue,
            mock_mr,
            "feature-branch",
            None,  # No pipeline error
            mock_user,
            prompt_only=False,
        )

        assert len(results) == 1
        assert results[0].discussion_id == "disc1"
        assert mock_resume_discussion.call_count == 2
        assert mock_get_result.call_count == 1


class TestAnalyzeFunction:
    """Test cases for the analyze function."""

    @pytest.fixture
    def mock_gitlab_client(self) -> Mock:
        """Create a mock GitLabClient."""
        client = Mock(spec=GitLabClient)
        return client

    @pytest.fixture
    def mock_project_info(self) -> Mock:
        """Create mock project info."""
        project = Mock()
        project.name = "test-project"
        project.git_url = "**************:test/test-project.git"
        return project

    @pytest.fixture
    def mock_user(self) -> Mock:
        """Create a mock user."""
        user = Mock(spec=CurrentUser)
        user.attributes = {"name": "Test User"}
        return user

    @pytest.fixture
    def mock_issue(self) -> NeiaIssue:
        """Create a mock issue."""
        return NeiaIssue(
            id="123",
            source="gitlab",
            title="Test Issue",
            description="Test description",
            comments=[],
        )

    @patch("neia.repository_analyzer.RepositoryAnalyzer")
    @patch("neia.repository_analyzer.os.getenv")
    @patch("neia.repository_analyzer.click.echo")
    def test_analyze_no_user(
        self,
        mock_echo: Mock,
        mock_getenv: Mock,
        mock_analyzer_class: Mock,
        mock_gitlab_client: Mock,
    ) -> None:
        """Test analyze function when user is not found."""
        mock_gitlab_client.get_user.return_value = None

        with pytest.raises(
            ValueError, match="Unable to authenticate with GitLab - user not found"
        ):
            analyze(
                mock_gitlab_client, "123", "feature-branch", None, None, False, False
            )

    @patch("neia.repository_analyzer.RepositoryAnalyzer")
    @patch("neia.repository_analyzer.os.getenv")
    @patch("neia.repository_analyzer.click.echo")
    def test_analyze_no_results(
        self,
        mock_echo: Mock,
        mock_getenv: Mock,
        mock_analyzer_class: Mock,
        mock_gitlab_client: Mock,
        mock_project_info: Mock,
        mock_user: Mock,
    ) -> None:
        """Test analyze function when no results are returned."""
        mock_gitlab_client.get_project.return_value = mock_project_info
        mock_gitlab_client.get_user.return_value = mock_user
        mock_gitlab_client.get_pipeline_error.return_value = None
        mock_getenv.return_value = "gpt-4o-mini"

        mock_analyzer = Mock()
        mock_analyzer.analyze.return_value = []
        mock_analyzer.get_repo.return_value = Mock()
        mock_analyzer_class.return_value = mock_analyzer

        analyze(mock_gitlab_client, "123", "feature-branch", None, None, False, False)

        mock_echo.assert_called_with("No changes to be done")

    @patch("neia.repository_analyzer.RepositoryAnalyzer")
    @patch("neia.repository_analyzer.os.getenv")
    @patch("neia.repository_analyzer.click.echo")
    def test_analyze_dry_run(
        self,
        mock_echo: Mock,
        mock_getenv: Mock,
        mock_analyzer_class: Mock,
        mock_gitlab_client: Mock,
        mock_project_info: Mock,
        mock_user: Mock,
        mock_issue: NeiaIssue,
    ) -> None:
        """Test analyze function in dry run mode."""
        mock_gitlab_client.get_project.return_value = mock_project_info
        mock_gitlab_client.get_user.return_value = mock_user
        mock_gitlab_client.get_pipeline_error.return_value = None
        mock_getenv.return_value = "gpt-4o-mini"

        mock_repo = Mock()
        mock_repo.working_dir = "/tmp/test-project"

        mock_analyzer = Mock()
        result = AnalysisResult(coder_response="Test changes", commit_id="abc123")
        mock_analyzer.analyze.return_value = [result]
        mock_analyzer.get_repo.return_value = mock_repo
        mock_analyzer_class.return_value = mock_analyzer

        analyze(
            mock_gitlab_client,
            "123",
            "feature-branch",
            mock_issue,
            None,
            dry_run=True,
            prompt_only=False,
        )

        mock_echo.assert_any_call(
            "Dry run mode - check changes in the repository at path: /tmp/test-project"
        )
        mock_echo.assert_any_call("Commit: abc123")
        mock_echo.assert_any_call("Test changes")

    @patch("neia.repository_analyzer.RepositoryAnalyzer")
    @patch("neia.repository_analyzer.os.getenv")
    @patch("neia.repository_analyzer.click.echo")
    def test_analyze_success_with_mr_creation(
        self,
        mock_echo: Mock,
        mock_getenv: Mock,
        mock_analyzer_class: Mock,
        mock_gitlab_client: Mock,
        mock_project_info: Mock,
        mock_user: Mock,
        mock_issue: NeiaIssue,
    ) -> None:
        """Test successful analyze function with MR creation."""
        mock_gitlab_client.get_project.return_value = mock_project_info
        mock_gitlab_client.get_user.return_value = mock_user
        mock_gitlab_client.get_pipeline_error.return_value = None
        mock_gitlab_client.submit_review_changes.return_value = (
            "https://gitlab.com/test/mr/1"
        )
        mock_getenv.return_value = "gpt-4o-mini"

        mock_repo = Mock()
        mock_repo.active_branch.name = "feature-branch"
        mock_repo.git.push = Mock()

        mock_analyzer = Mock()
        result = AnalysisResult(
            coder_response="Changes applied", title="Test MR Title", commit_id="abc123"
        )
        mock_analyzer.analyze.return_value = [result]
        mock_analyzer.get_repo.return_value = mock_repo
        mock_analyzer_class.return_value = mock_analyzer

        analyze(
            mock_gitlab_client,
            "123",
            "feature-branch",
            mock_issue,
            None,
            dry_run=False,
            prompt_only=False,
        )

        mock_repo.git.push.assert_called_once()
        mock_echo.assert_any_call("Changes were applied successfully")
        mock_echo.assert_any_call("Merge request: https://gitlab.com/test/mr/1")

    @patch("neia.repository_analyzer.RepositoryAnalyzer")
    @patch("neia.repository_analyzer.os.getenv")
    @patch("neia.repository_analyzer.click.echo")
    def test_analyze_with_error_result(
        self,
        mock_echo: Mock,
        mock_getenv: Mock,
        mock_analyzer_class: Mock,
        mock_gitlab_client: Mock,
        mock_project_info: Mock,
        mock_user: Mock,
    ) -> None:
        """Test analyze function when result contains error."""
        mock_gitlab_client.get_project.return_value = mock_project_info
        mock_gitlab_client.get_user.return_value = mock_user
        mock_gitlab_client.get_pipeline_error.return_value = None
        mock_getenv.return_value = "gpt-4o-mini"

        mock_analyzer = Mock()
        error_result = AnalysisResult(coder_response=None, error="Something went wrong")
        mock_analyzer.analyze.return_value = [error_result]
        mock_analyzer.get_repo.return_value = Mock()
        mock_analyzer_class.return_value = mock_analyzer

        analyze(mock_gitlab_client, "123", "feature-branch", None, None, False, False)

        mock_echo.assert_any_call("An error occurred")
        mock_echo.assert_any_call("Something went wrong")
