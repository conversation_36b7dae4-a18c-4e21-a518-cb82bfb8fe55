import os
from unittest.mock import Mock, patch

import pytest

from neia.issue_loader import get_issue_from_url
from neia.models import IssueComment, NeiaIssue


class TestGetIssueFromUrl:
    @pytest.fixture
    def sample_issue(self) -> NeiaIssue:
        """Sample NeiaIssue for testing."""
        return NeiaIssue(
            id="123",
            source="test",
            title="Test Issue",
            description="Test description",
            comments=[IssueComment(id=1, author="Test Author", body="Test comment")],
        )

    @pytest.fixture
    def gitlab_env_vars(self) -> dict[str, str]:
        """GitLab environment variables."""
        return {
            "GITLAB_URL": "https://gitlab.example.com",
            "GITLAB_TOKEN": "test_token",
        }

    @pytest.fixture
    def tuleap_env_vars(self) -> dict[str, str]:
        """Tuleap environment variables."""
        return {
            "TULEAP_API_URL": "https://tuleap.example.com",
            "TULEAP_USER": "test_user",
            "TULEAP_PASSWORD": "test_password",
        }

    @pytest.fixture
    def google_env_vars(self) -> dict[str, str]:
        """Google environment variables."""
        return {"GOOGLE_TOKEN_FILE": "test_token.json"}

    # GitLab Tests
    @patch.dict(
        os.environ,
        {"GITLAB_URL": "https://gitlab.example.com", "GITLAB_TOKEN": "test_token"},
    )
    @patch("neia.issue_loader.GitLabClient")
    def test_gitlab_issue_success(
        self, mock_gitlab_client: Mock, sample_issue: NeiaIssue
    ) -> None:
        """Test successful GitLab issue retrieval."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.get_issue.return_value = sample_issue
        mock_gitlab_client.return_value = mock_client_instance

        # Test
        url = "https://gitlab.example.com/project/repo/-/issues/123"
        project_id = "project/repo"

        result = get_issue_from_url(url, project_id)

        # Assertions
        mock_gitlab_client.assert_called_once_with(
            "https://gitlab.example.com", "test_token", "project/repo"
        )
        mock_client_instance.get_issue.assert_called_once_with("project/repo", "123")
        assert result == sample_issue
        assert result.url == "https://gitlab.example.com/project/repo/-/issues/123"

    @patch.dict(os.environ, {}, clear=True)
    def test_gitlab_missing_env_vars(self) -> None:
        """Test GitLab URL with missing environment variables."""
        url = "https://gitlab.example.com/project/repo/-/issues/123"
        project_id = "project/repo"

        with pytest.raises(Exception, match="The issue URL is not valid"):
            get_issue_from_url(url, project_id)

    @patch.dict(
        os.environ,
        {"GITLAB_URL": "https://gitlab.example.com", "GITLAB_TOKEN": "test_token"},
    )
    @patch("neia.issue_loader.GitLabClient")
    def test_gitlab_different_domain_no_match(
        self, mock_gitlab_client: Mock, sample_issue: NeiaIssue
    ) -> None:
        """Test GitLab client configured but URL doesn't match the configured domain."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.get_issue.return_value = sample_issue
        mock_gitlab_client.return_value = mock_client_instance

        # Test with different domain
        url = "https://different-gitlab.com/project/repo/-/issues/123"
        project_id = "project/repo"

        with pytest.raises(Exception, match="The issue URL is not valid"):
            get_issue_from_url(url, project_id)

        # GitLab client should not be called since URL doesn't match
        mock_gitlab_client.assert_not_called()

    # Google Docs Tests
    @patch.dict(os.environ, {"GOOGLE_TOKEN_FILE": "test_token.json"})
    @patch("neia.issue_loader.GoogleClient")
    def test_google_docs_issue_success(
        self, mock_google_client: Mock, sample_issue: NeiaIssue
    ) -> None:
        """Test successful Google Docs issue retrieval."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.auth.return_value = True
        mock_client_instance.get_issue.return_value = sample_issue
        mock_google_client.return_value = mock_client_instance

        # Test
        url = "https://docs.google.com/document/d/1234567890abcdef/edit"
        project_id = "test_project"

        result = get_issue_from_url(url, project_id)

        # Assertions
        mock_google_client.assert_called_once_with("test_token.json")
        mock_client_instance.auth.assert_called_once()
        mock_client_instance.get_issue.assert_called_once_with("1234567890abcdef")
        assert result == sample_issue
        assert result.url == "https://docs.google.com/document/d/1234567890abcdef/edit"

    @patch.dict(os.environ, {"GOOGLE_TOKEN_FILE": "test_token.json"})
    @patch("neia.issue_loader.GoogleClient")
    def test_google_docs_auth_failure(self, mock_google_client: Mock) -> None:
        """Test Google Docs authentication failure."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.auth.return_value = False
        mock_google_client.return_value = mock_client_instance

        # Test
        url = "https://docs.google.com/document/d/1234567890abcdef/edit"
        project_id = "test_project"

        with pytest.raises(
            Exception, match="The Google token does not exist or is not valid"
        ):
            get_issue_from_url(url, project_id)

        mock_client_instance.auth.assert_called_once()

    @patch.dict(os.environ, {}, clear=True)
    @patch("neia.issue_loader.GoogleClient")
    def test_google_docs_default_token_file(
        self, mock_google_client: Mock, sample_issue: NeiaIssue
    ) -> None:
        """Test Google Docs with default token file when env var not set."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.auth.return_value = True
        mock_client_instance.get_issue.return_value = sample_issue
        mock_google_client.return_value = mock_client_instance

        # Test
        url = "https://docs.google.com/document/d/1234567890abcdef/edit"
        project_id = "test_project"

        get_issue_from_url(url, project_id)

        # Should use default token.json
        mock_google_client.assert_called_once_with("token.json")

    def test_google_docs_url_variations(self) -> None:
        """Test various Google Docs URL formats."""
        test_cases = [
            "https://docs.google.com/document/d/1234567890abcdef/edit",
            "https://docs.google.com/document/d/1234567890abcdef/edit?usp=sharing",
            "https://docs.google.com/document/d/1234567890abcdef/",
            "http://docs.google.com/document/d/1234567890abcdef/edit",
        ]

        with patch.dict(os.environ, {"GOOGLE_TOKEN_FILE": "test_token.json"}):
            with patch("neia.issue_loader.GoogleClient") as mock_google_client:
                mock_client_instance = Mock()
                mock_client_instance.auth.return_value = True
                mock_client_instance.get_issue.return_value = NeiaIssue(
                    id="test",
                    source="gdocs",
                    title="Test",
                    description="Test",
                    comments=[],
                )
                mock_google_client.return_value = mock_client_instance

                for url in test_cases:
                    result = get_issue_from_url(url, "test_project")
                    assert result is not None
                    mock_client_instance.get_issue.assert_called_with(
                        "1234567890abcdef"
                    )

    # Tuleap Tests
    @patch.dict(
        os.environ,
        {
            "TULEAP_API_URL": "https://tuleap.example.com",
            "TULEAP_USER": "test_user",
            "TULEAP_PASSWORD": "test_password",
            "TULEAP_DESCRIPTION_FIELD_ID": "1234",
            "TULEAP_ASSIGNEE_FIELD_ID": "1235",
        },
    )
    @patch("neia.issue_loader.TuleapClient")
    def test_tuleap_issue_success(
        self, mock_tuleap_client: Mock, sample_issue: NeiaIssue
    ) -> None:
        """Test successful Tuleap issue retrieval."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.get_issue.return_value = sample_issue
        mock_tuleap_client.return_value = mock_client_instance

        # Test
        url = "https://tuleap.example.com/plugins/tracker/?aid=123"
        project_id = "test_project"

        result = get_issue_from_url(url, project_id)

        # Assertions
        mock_tuleap_client.assert_called_once_with(
            "https://tuleap.example.com",
            "test_user",
            "test_password",
            1234,
            1235,
        )
        mock_client_instance.get_issue.assert_called_once_with("123")
        assert result == sample_issue
        assert result.url == "https://tuleap.example.com/plugins/tracker/?aid=123"

    @patch.dict(
        os.environ,
        {
            "TULEAP_API_URL": "https://tuleap.example.com"
            # Missing TULEAP_USER and TULEAP_PASSWORD
        },
        clear=True,
    )
    def test_tuleap_missing_credentials(self) -> None:
        """Test Tuleap URL with missing credentials."""
        url = "https://tuleap.example.com/plugins/tracker/?aid=123"
        project_id = "test_project"

        with pytest.raises(
            Exception,
            match="Please provide 'TULEAP_API_URL', 'TULEAP_USER', 'TULEAP_PASSWORD' and 'TULEAP_DESCRIPTION_FIELD_ID'.",
        ):
            get_issue_from_url(url, project_id)

    @patch.dict(os.environ, {}, clear=True)
    def test_tuleap_no_api_url(self) -> None:
        """Test Tuleap URL when TULEAP_API_URL is not set."""
        url = "https://tuleap.example.com/plugins/tracker/?aid=123"
        project_id = "test_project"

        with pytest.raises(Exception, match="The issue URL is not valid"):
            get_issue_from_url(url, project_id)

    # Priority Tests (GitLab takes precedence over others)
    @patch.dict(
        os.environ,
        {
            "GITLAB_URL": "https://gitlab.example.com",
            "GITLAB_TOKEN": "test_token",
            "TULEAP_API_URL": "https://gitlab.example.com",  # Same domain
            "TULEAP_USER": "test_user",
            "TULEAP_PASSWORD": "test_password",
            "TULEAP_DESCRIPTION_FIELD_ID": "1234",
        },
        clear=True,
    )
    @patch("neia.issue_loader.GitLabClient")
    @patch("neia.issue_loader.TuleapClient")
    def test_gitlab_priority_over_tuleap(
        self,
        mock_tuleap_client: Mock,
        mock_gitlab_client: Mock,
        sample_issue: NeiaIssue,
    ) -> None:
        """Test that GitLab takes priority when URLs could match multiple services."""
        # Setup mocks
        ******************** = Mock()
        ********************.get_issue.return_value = sample_issue
        mock_gitlab_client.return_value = ********************

        # Test with GitLab URL format
        url = "https://gitlab.example.com/project/repo/-/issues/123"
        project_id = "project/repo"

        result = get_issue_from_url(url, project_id)

        # GitLab should be called, Tuleap should not
        mock_gitlab_client.assert_called_once()
        mock_tuleap_client.assert_not_called()
        assert result == sample_issue

    # Error Cases
    def test_invalid_url_format(self) -> None:
        """Test completely invalid URL format."""
        url = "not-a-valid-url"
        project_id = "test_project"

        with pytest.raises(Exception, match="The issue URL is not valid"):
            get_issue_from_url(url, project_id)

    def test_unsupported_domain(self) -> None:
        """Test URL from unsupported domain."""
        url = "https://unsupported.example.com/issues/123"
        project_id = "test_project"

        with pytest.raises(Exception, match="The issue URL is not valid"):
            get_issue_from_url(url, project_id)

    @patch.dict(
        os.environ,
        {"GITLAB_URL": "https://gitlab.example.com", "GITLAB_TOKEN": "test_token"},
        clear=True,
    )
    @patch("neia.issue_loader.GitLabClient")
    def test_client_exception_propagation(self, mock_gitlab_client: Mock) -> None:
        """Test that exceptions from clients are properly propagated."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.get_issue.side_effect = Exception("GitLab API error")
        mock_gitlab_client.return_value = mock_client_instance

        # Test
        url = "https://gitlab.example.com/project/repo/-/issues/123"
        project_id = "project/repo"

        with pytest.raises(Exception, match="GitLab API error"):
            get_issue_from_url(url, project_id)

    # Edge Cases
    @patch.dict(
        os.environ,
        {"GITLAB_URL": "https://gitlab.example.com", "GITLAB_TOKEN": "test_token"},
        clear=True,
    )
    @patch("neia.issue_loader.GitLabClient")
    def test_gitlab_url_with_special_characters(
        self, mock_gitlab_client: Mock, sample_issue: NeiaIssue
    ) -> None:
        """Test GitLab URL with special characters in project path."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.get_issue.return_value = sample_issue
        mock_gitlab_client.return_value = mock_client_instance

        # Test with project path containing special characters
        url = "https://gitlab.example.com/group.name/project-name/-/issues/456"
        project_id = "group.name/project-name"

        result = get_issue_from_url(url, project_id)

        mock_client_instance.get_issue.assert_called_once_with(
            "group.name/project-name", "456"
        )
        assert result.url == url

    def test_empty_url(self) -> None:
        """Test empty URL string."""
        with pytest.raises(Exception, match="The issue URL is not valid"):
            get_issue_from_url("", "test_project")

    # Integration-like tests with multiple services configured
    @patch.dict(
        os.environ,
        {
            "GITLAB_URL": "https://gitlab.example.com",
            "GITLAB_TOKEN": "test_token",
            "TULEAP_API_URL": "https://tuleap.example.com",
            "TULEAP_USER": "test_user",
            "TULEAP_PASSWORD": "test_password",
            "GOOGLE_TOKEN_FILE": "test_token.json",
        },
        clear=True,
    )
    @patch("neia.issue_loader.GoogleClient")
    def test_google_docs_with_all_services_configured(
        self, mock_google_client: Mock, sample_issue: NeiaIssue
    ) -> None:
        """Test Google Docs URL when all services are configured."""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client_instance.auth.return_value = True
        mock_client_instance.get_issue.return_value = sample_issue
        mock_google_client.return_value = mock_client_instance

        # Test with Google Docs URL
        url = "https://docs.google.com/document/d/1234567890abcdef/edit"
        project_id = "test_project"

        result = get_issue_from_url(url, project_id)

        # Only Google client should be called
        mock_google_client.assert_called_once_with("test_token.json")
        assert result == sample_issue
        assert result.url == url
