from typing import Union
from unittest.mock import Mock, patch

import pytest

from neia.models import NeiaIssue
from neia.tuleap_client import TuleapClient


class TestTuleapClientIntegration:
    @pytest.fixture
    def sample_tuleap_artifact(
        self,
    ) -> dict[str, Union[str, int, list[dict[str, Union[str, int]]]]]:
        """Sample Tuleap artifact data for testing."""
        return {
            "id": 42,
            "title": "Sample Issue",
            "values": [
                {"field_id": 1234, "value": "Sample description"},
                {"field_id": 12116, "value": "Other field value"},
            ],
        }

    @pytest.fixture
    def sample_tuleap_artifact_no_description(
        self,
    ) -> dict[str, Union[str, int, list[dict[str, Union[str, int]]]]]:
        """Sample Tuleap artifact data without description for testing."""
        return {
            "id": 43,
            "title": "Sample Issue No Description",
            "values": [{"field_id": 12116, "value": "Other field value"}],
        }

    @pytest.fixture
    def client(self) -> TuleapClient:
        return TuleapClient(
            host="https://test.tuleap.org",
            username="testuser",
            password="testpass",
            description_field_id=1234,
            assignee_field_id=1235,
        )

    @pytest.mark.unit
    @patch("requests.get")
    def test_get_issue_with_sample_data(
        self,
        mock_get: Mock,
        client: TuleapClient,
        sample_tuleap_artifact: dict[
            str, Union[str, int, list[dict[str, Union[str, int]]]]
        ],
        mock_response: Mock,
    ) -> None:
        mock_response.json.return_value = sample_tuleap_artifact
        mock_get.return_value = mock_response

        result = client.get_issue(artifact_id="42")

        assert isinstance(result, NeiaIssue)
        assert result.id == 42
        assert result.title == "Sample Issue"
        assert result.description == "Sample description"
        assert result.source == "tuleap"

    @pytest.mark.unit
    @patch("requests.get")
    def test_get_issue_with_no_description_data(
        self,
        mock_get: Mock,
        client: TuleapClient,
        sample_tuleap_artifact_no_description: dict[
            str, Union[str, int, list[dict[str, Union[str, int]]]]
        ],
        mock_response: Mock,
    ) -> None:
        mock_response.json.return_value = sample_tuleap_artifact_no_description
        mock_get.return_value = mock_response

        result = client.get_issue(artifact_id="43")

        assert isinstance(result, NeiaIssue)
        assert result.id == 43
        assert result.title == "Sample Issue No Description"
        assert result.description == ""  # Should default to empty string
        assert result.source == "tuleap"

    @pytest.mark.unit
    def test_constants_and_configuration(self, client: TuleapClient) -> None:
        assert client.description_field_id == 1234
        assert client.host.startswith("https://")
        assert len(client.username) > 0
        assert len(client.password) > 0
