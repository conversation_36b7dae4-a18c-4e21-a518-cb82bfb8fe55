"""Tests for repository utilities."""

import tempfile
from collections.abc import Generator
from pathlib import Path
from unittest.mock import Mock, patch

import git
import pytest

from neia.local_repository_manager import LocalRepositoryManager


class TestRepositoryManager:
    """Test suite for RepositoryManager class."""

    @pytest.fixture
    def temp_base_path(self) -> Generator[str, None, None]:
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def repo_manager(self, temp_base_path: str) -> LocalRepositoryManager:
        """Create a RepositoryManager instance for testing."""
        return LocalRepositoryManager(temp_base_path)

    def test_init(self, temp_base_path: str) -> None:
        """Test RepositoryManager initialization."""
        repo_manager = LocalRepositoryManager(temp_base_path)

        assert repo_manager.base_path == Path(temp_base_path)
        assert repo_manager.base_path.exists()

    def test_get_repo_path(self, repo_manager: LocalRepositoryManager) -> None:
        """Test get_repo_path method."""
        project_name = "test-project"
        expected_path = repo_manager.base_path / project_name

        result = repo_manager.get_repo_path(project_name)

        assert result == expected_path

    @patch("git.Repo.clone_from")
    def test_ensure_local_repository_clone_new(
        self, mock_clone: Mock, repo_manager: LocalRepositoryManager
    ) -> None:
        """Test ensure_local_repository when repository doesn't exist locally."""
        project_name = "new-project"
        git_url = "**************:test/repo.git"
        mock_repo = Mock(spec=git.Repo)
        mock_clone.return_value = mock_repo

        # Mock config_writer to avoid actual git configuration
        mock_config_writer = Mock()
        mock_repo.config_writer.return_value = mock_config_writer
        mock_config_writer.set_value.return_value = mock_config_writer

        result = repo_manager.ensure_local_repository(project_name, git_url)

        assert result == mock_repo
        mock_clone.assert_called_once_with(
            git_url, str(repo_manager.get_repo_path(project_name))
        )
        # Verify git configuration is set
        mock_repo.config_writer.assert_called()

    @patch("git.Repo")
    def test_ensure_local_repository_update_existing(
        self, mock_repo_class: Mock, repo_manager: LocalRepositoryManager
    ) -> None:
        """Test ensure_local_repository when repository exists locally."""
        project_name = "existing-project"
        git_url = "**************:test/repo.git"

        # Create the repository directory
        repo_path = repo_manager.get_repo_path(project_name)
        repo_path.mkdir(parents=True)

        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo

        # Mock remote and branch operations
        mock_origin = Mock()
        mock_repo.remotes.origin = mock_origin

        # Mock refs to include the current branch
        mock_main_ref = Mock()
        mock_main_ref.name = "origin/main"
        mock_origin.refs = [mock_main_ref]

        mock_branch = Mock()
        mock_branch.name = "main"
        mock_repo.active_branch = mock_branch

        # Mock config_writer
        mock_config_writer = Mock()
        mock_repo.config_writer.return_value = mock_config_writer
        mock_config_writer.set_value.return_value = mock_config_writer

        result = repo_manager.ensure_local_repository(project_name, git_url)

        assert result == mock_repo
        mock_repo_class.assert_called_once_with(str(repo_path))
        mock_origin.fetch.assert_called_once()
        mock_origin.pull.assert_called_once()  # Should be called since origin/main exists

    def test_ensure_branch_existing_remote(
        self, repo_manager: LocalRepositoryManager
    ) -> None:
        """Test ensure_branch when branch exists remotely."""
        mock_repo = Mock()
        branch_name = "feature-branch"

        # Mock git branch command output
        mock_repo.git.branch.return_value = "  origin/main\n  origin/feature-branch\n"

        # Mock existing local heads
        mock_repo.heads = []  # No local branches

        repo_manager.ensure_branch(mock_repo, branch_name)

        # Should checkout new branch from remote
        mock_repo.git.checkout.assert_called_with(
            "-b", branch_name, f"origin/{branch_name}"
        )
        mock_repo.git.reset.assert_called_with("--hard", f"origin/{branch_name}")

    def test_ensure_branch_existing_local(
        self, repo_manager: LocalRepositoryManager
    ) -> None:
        """Test ensure_branch when branch exists locally."""
        mock_repo = Mock()
        branch_name = "feature-branch"

        # Mock git branch command output
        mock_repo.git.branch.return_value = "  origin/main\n  origin/feature-branch\n"

        # Mock existing local heads - branch exists locally
        mock_branch = Mock()
        mock_branch.name = branch_name
        mock_heads = Mock()
        mock_heads.__contains__ = Mock(side_effect=lambda x: x == branch_name)
        mock_repo.heads = mock_heads

        repo_manager.ensure_branch(mock_repo, branch_name)

        # Should checkout existing branch (not create new one)
        mock_repo.git.checkout.assert_called_with(branch_name)
        mock_repo.git.reset.assert_called_with("--hard", f"origin/{branch_name}")

    def test_ensure_branch_new_from_main(
        self, repo_manager: LocalRepositoryManager
    ) -> None:
        """Test ensure_branch when creating new branch from main."""
        mock_repo = Mock()
        branch_name = "new-feature"

        # Mock git branch command output (no remote branch)
        mock_repo.git.branch.return_value = "  origin/main\n"

        # Mock existing local heads
        mock_repo.heads = []

        repo_manager.ensure_branch(mock_repo, branch_name)

        # Should create new branch from main
        mock_repo.git.checkout.assert_any_call("main")
        mock_repo.git.pull.assert_called_with("origin", "main")
        mock_repo.create_head.assert_called_with(branch_name)
        mock_repo.git.checkout.assert_any_call(branch_name)
