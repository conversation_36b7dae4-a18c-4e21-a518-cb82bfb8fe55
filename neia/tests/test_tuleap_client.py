from unittest.mock import Mock, patch

import pytest
import requests
from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from neia.models import NeiaIssue
from neia.tuleap_client import TuleapClient


class TestTuleapClient:
    @pytest.fixture
    def client(self) -> TuleapClient:
        """Create a TuleapClient instance for testing."""
        return TuleapClient(
            host="https://test.tuleap.org",
            username="testuser",
            password="testpass",
            description_field_id=1234,
            assignee_field_id=1235,
        )

    def test_init(self, client: TuleapClient) -> None:
        """Test TuleapClient initialization."""
        assert client.host == "https://test.tuleap.org"
        assert client.username == "testuser"
        assert client.password == "testpass"

    def test_get_basic_auth(self, client: TuleapClient) -> None:
        """Test basic authentication creation."""
        auth = client.get_basic_auth()
        assert isinstance(auth, HTTPBasicAuth)
        assert auth.username == "testuser"
        assert auth.password == "testpass"

    def test_get_headers(self, client: <PERSON>leapClient) -> None:
        """Test headers creation."""
        headers = client.get_headers()
        assert headers == {"Content-Type": "application/json"}

    @patch("requests.get")
    def test_get_artifacts_success(self, mock_get: Mock, client: TuleapClient) -> None:
        """Test successful artifacts retrieval."""
        # Mock response
        mock_response = Mock()
        mock_response.json.return_value = [
            {"id": 1, "title": "Test Artifact 1"},
            {"id": 2, "title": "Test Artifact 2"},
        ]
        mock_get.return_value = mock_response

        result = client.get_artifacts(tracker_id=123)

        # Verify the request was made correctly
        mock_get.assert_called_once_with(
            "https://test.tuleap.org/api/trackers/123/artifacts",
            headers={"Content-Type": "application/json"},
            auth=client.get_basic_auth(),
        )

        # Verify the result
        assert result == [
            {"id": 1, "title": "Test Artifact 1"},
            {"id": 2, "title": "Test Artifact 2"},
        ]

    @patch("requests.get")
    def test_get_issue_success_with_description(
        self, mock_get: Mock, client: TuleapClient
    ) -> None:
        """Test successful issue retrieval with description."""
        # Mock response with description
        mock_response = Mock()
        mock_response.json.return_value = {
            "id": 42,
            "title": "Test Issue",
            "values": [
                {"field_id": 1234, "value": "This is a test description"},
                {"field_id": 12116, "value": "Other field value"},
            ],
        }
        mock_get.return_value = mock_response

        result = client.get_issue(artifact_id="42")

        # Verify the request was made correctly
        mock_get.assert_called_once_with(
            "https://test.tuleap.org/api/artifacts/42",
            headers={"Content-Type": "application/json"},
            auth=client.get_basic_auth(),
        )

        # Verify the result
        assert isinstance(result, NeiaIssue)
        assert result.id == 42
        assert result.source == "tuleap"
        assert result.title == "Test Issue"
        assert result.description == "This is a test description"
        assert result.comments == []

    @patch("requests.get")
    def test_get_issue_success_without_description(
        self, mock_get: Mock, client: TuleapClient
    ) -> None:
        """Test successful issue retrieval without description field."""
        # Mock response without description field
        mock_response = Mock()
        mock_response.json.return_value = {
            "id": 42,
            "title": "Test Issue",
            "values": [{"field_id": 12116, "value": "Other field value"}],
        }
        mock_get.return_value = mock_response

        result = client.get_issue(artifact_id="42")

        # Verify the result - should handle missing description gracefully
        assert isinstance(result, NeiaIssue)
        assert result.id == 42
        assert result.source == "tuleap"
        assert result.title == "Test Issue"
        assert result.description == ""  # Should default to empty string
        assert result.comments == []

    @patch("requests.get")
    def test_get_issue_success_with_none_description(
        self, mock_get: Mock, client: TuleapClient
    ) -> None:
        """Test successful issue retrieval with None description value."""
        # Mock response with None description
        mock_response = Mock()
        mock_response.json.return_value = {
            "id": 42,
            "title": "Test Issue",
            "values": [
                {"field_id": 12115, "value": None},
                {"field_id": 12116, "value": "Other field value"},
            ],
        }
        mock_get.return_value = mock_response

        result = client.get_issue(artifact_id="42")

        # Verify the result - should handle None description gracefully
        assert isinstance(result, NeiaIssue)
        assert result.id == 42
        assert result.source == "tuleap"
        assert result.title == "Test Issue"
        assert result.description == ""  # Should default to empty string
        assert result.comments == []

    @patch("requests.get")
    def test_get_issue_error_response(
        self, mock_get: Mock, client: TuleapClient
    ) -> None:
        """Test error handling when Tuleap API returns an error."""
        # Mock error response
        mock_response = Mock()
        mock_response.json.return_value = {"error": "Artifact not found"}
        mock_get.return_value = mock_response

        with pytest.raises(Exception) as exc_info:
            client.get_issue(artifact_id="999")

        assert "Error while fetching issue from Tuleap: Artifact not found" in str(
            exc_info.value
        )

    @patch("requests.get")
    def test_get_issue_network_error(
        self, mock_get: Mock, client: TuleapClient
    ) -> None:
        """Test handling of network errors."""
        # Mock network error
        mock_get.side_effect = requests.ConnectionError("Connection failed")

        with pytest.raises(requests.ConnectionError):
            client.get_issue(artifact_id="42")

    @patch("requests.get")
    def test_get_issue_json_decode_error(
        self, mock_get: Mock, client: TuleapClient
    ) -> None:
        """Test handling of invalid JSON responses."""
        # Mock invalid JSON response
        mock_response = Mock()
        mock_response.json.side_effect = ValueError("Invalid JSON")
        mock_get.return_value = mock_response

        with pytest.raises(ValueError):
            client.get_issue(artifact_id="42")

    @patch("requests.get")
    def test_get_issue_with_empty_values(
        self, mock_get: Mock, client: TuleapClient
    ) -> None:
        """Test issue retrieval with empty values array."""
        # Mock response with empty values
        mock_response = Mock()
        mock_response.json.return_value = {
            "id": 42,
            "title": "Test Issue",
            "values": [],
        }
        mock_get.return_value = mock_response

        result = client.get_issue(artifact_id="42")

        assert isinstance(result, NeiaIssue)
        assert result.id == 42
        assert result.description == ""  # Should default to empty string

    def test_description_field_id_constant(self, client: TuleapClient) -> None:
        """Test that the description field ID constant is correctly set."""
        assert client.description_field_id == 1234

    @patch("requests.get")
    def test_multiple_get_issue_calls(
        self, mock_get: Mock, client: TuleapClient
    ) -> None:
        """Test multiple calls to get_issue with different artifacts."""
        # Mock responses for different artifacts
        responses = [
            {
                "id": 1,
                "title": "First Issue",
                "values": [{"field_id": 1234, "value": "First description"}],
            },
            {
                "id": 2,
                "title": "Second Issue",
                "values": [{"field_id": 1234, "value": "Second description"}],
            },
        ]

        mock_response = Mock()
        mock_response.json.side_effect = responses
        mock_get.return_value = mock_response

        # Make multiple calls
        result1 = client.get_issue(artifact_id="1")
        result2 = client.get_issue(artifact_id="2")

        # Verify both calls were made correctly
        assert mock_get.call_count == 2
        assert result1.id == 1
        assert result1.title == "First Issue"
        assert result1.description == "First description"
        assert result2.id == 2
        assert result2.title == "Second Issue"
        assert result2.description == "Second description"
