from datetime import datetime
from unittest.mock import Mock, patch

import pytest
from gitlab.v4.objects import ProjectMergeRequest

from neia.code_reviewer import CodeReviewer
from neia.gitlab_client import GitLabClient
from neia.models import (
    QualityMetric,
    ReviewComment,
    ReviewCommentSeverity,
    ReviewCommentType,
    ReviewResults,
)


class TestCodeReviewer:
    """Test suite for CodeReviewer class."""

    @pytest.fixture
    def mock_gitlab_client(self) -> <PERSON><PERSON>:
        """Create a mock GitLab client."""
        mock_client = Mock(spec=GitLabClient)
        mock_project = Mock()
        mock_project.name = "test-project"
        mock_project.git_url = "**************:test/repo.git"
        mock_client.get_project.return_value = mock_project
        return mock_client

    @pytest.fixture
    def mock_repository_manager(self) -> Mock:
        """Create a mock repository manager."""
        mock_repo_manager = Mock()
        mock_repo = Mock()
        mock_repo.working_dir = "/tmp/test-project"
        mock_repo_manager.ensure_local_repository.return_value = mock_repo
        return mock_repo_manager

    @pytest.fixture
    def code_reviewer(self, mock_gitlab_client: Mock) -> CodeReviewer:
        """Create a CodeReviewer instance for testing."""
        with patch("neia.code_reviewer.LocalRepositoryManager"):
            return CodeReviewer(mock_gitlab_client, "test-project-id", "gpt-4o-mini")

    @pytest.fixture
    def mock_mr(self) -> Mock:
        """Create a mock merge request."""
        mr = Mock(spec=ProjectMergeRequest)
        mr.id = "123"
        mr.iid = "1"
        mr.title = "Test MR"
        mr.description = "Test description"
        mr.source_branch = "feature"
        mr.target_branch = "main"
        mr.state = "opened"
        mr.project_id = "456"
        return mr

    def test_init(self, mock_gitlab_client: Mock) -> None:
        """Test CodeReviewer initialization."""
        with patch("neia.code_reviewer.LocalRepositoryManager") as mock_repo_manager:
            reviewer = CodeReviewer(
                mock_gitlab_client, "test-project-id", "gpt-4o-mini", "projects"
            )

            assert reviewer.gitlab_client == mock_gitlab_client
            assert reviewer.project_id == "test-project-id"
            assert reviewer.model_name == "gpt-4o-mini"
            mock_repo_manager.assert_called_once_with("projects")
            mock_gitlab_client.get_project.assert_called_once_with("test-project-id")

    def test_analyze_merge_request_not_found(
        self, code_reviewer: CodeReviewer, mock_gitlab_client: Mock
    ) -> None:
        """Test analyze_merge_request when MR is not found."""
        mock_gitlab_client.get_merge_request.return_value = None

        result = code_reviewer.analyze_merge_request(mr_id="123")

        assert result is None
        mock_gitlab_client.get_merge_request.assert_called_once_with(
            mr_id="123", mr_iid=None
        )

    def test_analyze_merge_request_not_opened(
        self, code_reviewer: CodeReviewer, mock_gitlab_client: Mock, mock_mr: Mock
    ) -> None:
        """Test analyze_merge_request when MR is not in opened state."""
        mock_mr.state = "closed"
        mock_gitlab_client.get_merge_request.return_value = mock_mr

        result = code_reviewer.analyze_merge_request(mr_id="123")

        assert result is None

    def test_analyze_merge_request_no_parameters(
        self, code_reviewer: CodeReviewer
    ) -> None:
        """Test analyze_merge_request when no MR ID or IID is provided."""
        result = code_reviewer.analyze_merge_request()

        assert result is None

    def test_is_mr_too_large(self, code_reviewer: CodeReviewer) -> None:
        """Test _is_mr_too_large method."""
        small_changes = {
            "changes": [
                {"new_path": "file1.py", "diff": "line1\nline2\nline3"},
                {"new_path": "file2.py", "diff": "line1\nline2"},
            ]
        }
        assert not code_reviewer._is_mr_too_large(small_changes)

        large_file_changes = {
            "changes": [{"new_path": f"file{i}.py", "diff": "line1"} for i in range(60)]
        }
        assert code_reviewer._is_mr_too_large(large_file_changes)

        large_diff_changes = {
            "changes": [
                {
                    "new_path": "file1.py",
                    "diff": "\n".join([f"line{i}" for i in range(6000)]),
                }
            ]
        }
        assert code_reviewer._is_mr_too_large(large_diff_changes)

    def test_create_large_mr_review(
        self, code_reviewer: CodeReviewer, mock_mr: Mock
    ) -> None:
        """Test _create_large_mr_review method."""
        result = code_reviewer._create_large_mr_review(mock_mr)

        assert isinstance(result, ReviewResults)
        assert result.mr_id == "123"
        assert result.mr_iid == "1"
        assert result.overall_score == 5.0
        assert result.summary and "trop volumineuse" in result.summary
        assert result.quality_metrics and len(result.quality_metrics) == 1
        assert (
            result.quality_metrics
            and "Taille de la MR" in result.quality_metrics[0].name
        )

    @patch.object(CodeReviewer, "_get_mr_changes")
    @patch.object(CodeReviewer, "_is_mr_too_large")
    @patch.object(CodeReviewer, "_create_large_mr_review")
    def test_analyze_large_mr(
        self,
        mock_create_large_review: Mock,
        mock_is_too_large: Mock,
        mock_get_changes: Mock,
        code_reviewer: CodeReviewer,
        mock_gitlab_client: Mock,
        mock_mr: Mock,
    ) -> None:
        """Test analyze_merge_request with large MR."""
        mock_gitlab_client.get_merge_request.return_value = mock_mr
        mock_gitlab_client.get_project.return_value = Mock()
        mock_get_changes.return_value = {"changes": []}
        mock_is_too_large.return_value = True

        expected_result = ReviewResults(
            mr_id="123",
            mr_iid="1",
            overall_score=5.0,
            summary="test",
            inline_comments=[],
            quality_metrics=[],
            definition_of_done_issues=[],
            recommendations=[],
            analysis_timestamp=datetime.now().isoformat(),
        )
        mock_create_large_review.return_value = expected_result

        result = code_reviewer.analyze_merge_request(mr_id="123")

        assert result == expected_result
        mock_create_large_review.assert_called_once_with(mock_mr)

    def test_create_fallback_metrics(self, code_reviewer: CodeReviewer) -> None:
        """Test _create_fallback_metrics method."""
        metrics = code_reviewer._create_fallback_metrics("Test reason")

        assert len(metrics) == 1
        assert metrics[0].name == "Code Quality"
        assert metrics[0].score == 5.0
        assert "Test reason" in metrics[0].description

    def test_format_inline_comment(self, code_reviewer: CodeReviewer) -> None:
        """Test _format_inline_comment method."""
        comment = ReviewComment(
            file_path="test.py",
            line_number=10,
            comment_type=ReviewCommentType.ISSUE,
            message="Test issue",
            suggestion="Fix this",
            severity=ReviewCommentSeverity.MAJOR,
        )

        result = code_reviewer._format_inline_comment(comment)

        assert "*[NEIA]* **issue** (non-blocking): Test issue" in result
        assert "Fix this" in result

    def test_format_summary_comment(self, code_reviewer: CodeReviewer) -> None:
        """Test _format_summary_comment method."""
        review_result = ReviewResults(
            mr_id="123",
            mr_iid="1",
            overall_score=8.5,
            summary="Good quality code",
            inline_comments=[],
            quality_metrics=[
                QualityMetric(
                    name="Code Quality",
                    score=8.5,
                    description="Well structured",
                    issues=[],
                )
            ],
            definition_of_done_issues=[],
            recommendations=["Keep up the good work"],
            analysis_timestamp="2023-01-01T00:00:00",
        )

        result = code_reviewer._format_summary_comment(review_result)

        assert "## *[NEIA]* Automated Code Review" in result
        assert "**Score Global: 8.5/10**" in result
        assert "Good quality code" in result
        assert "Code Quality" in result
        assert "Keep up the good work" in result

    def test_get_mr_changes_error_handling(
        self, code_reviewer: CodeReviewer, mock_mr: Mock
    ) -> None:
        """Test error handling in _get_mr_changes method."""
        mock_mr.changes.side_effect = Exception("API Error")

        result = code_reviewer._get_mr_changes(mock_mr)

        assert result is None
