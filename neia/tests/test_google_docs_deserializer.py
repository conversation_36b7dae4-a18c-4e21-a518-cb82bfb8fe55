from typing import cast

import pytest

from neia.google_docs_deserializer import (
    ContentElement,
    ContentElementData,
    GoogleDocsDeserializer,
    Paragraph,
    ParagraphData,
    ParagraphElement,
    ParagraphElementData,
    TextRun,
    TextRunData,
)


class TestTextRun:
    """Test cases for TextRun Pydantic dataclass."""

    def test_create_with_content(self) -> None:
        """Test creating TextRun with valid content."""
        text_run = TextRun(content="Hello World")
        assert text_run.content == "Hello World"

    def test_create_with_default(self) -> None:
        """Test creating TextRun with default content."""
        text_run = TextRun()
        assert text_run.content == ""

    def test_validation_with_none(self) -> None:
        """Test validation converts None to empty string."""
        text_run = TextRun(content=None)  # type: ignore
        assert text_run.content == ""

    def test_validation_with_int(self) -> None:
        """Test validation converts integer to string."""
        text_run = TextRun(content=123)  # type: ignore
        assert text_run.content == "123"

    def test_validation_with_bool(self) -> None:
        """Test validation converts boolean to string."""
        text_run = TextRun(content=True)  # type: ignore
        assert text_run.content == "True"

    def test_validation_with_list(self) -> None:
        """Test validation converts list to string."""
        text_run = TextRun(content=[1, 2, 3])  # type: ignore
        assert text_run.content == "[1, 2, 3]"

    def test_dataclass_dict_access(self) -> None:
        """Test that dataclass provides __dict__ access."""
        text_run = TextRun(content="test")
        assert text_run.__dict__ == {"content": "test"}


class TestParagraphElement:
    """Test cases for ParagraphElement Pydantic dataclass."""

    def test_create_with_text_run(self) -> None:
        """Test creating ParagraphElement with TextRun."""
        text_run = TextRun(content="Hello")
        para_elem = ParagraphElement(textRun=text_run)
        assert para_elem.textRun == text_run

    def test_create_with_default(self) -> None:
        """Test creating ParagraphElement with default None textRun."""
        para_elem = ParagraphElement()
        assert para_elem.textRun is None

    def test_get_text_content_with_text_run(self) -> None:
        """Test getting text content when textRun exists."""
        text_run = TextRun(content="Hello World")
        para_elem = ParagraphElement(textRun=text_run)
        assert para_elem.get_text_content() == "Hello World"

    def test_get_text_content_without_text_run(self) -> None:
        """Test getting text content when textRun is None."""
        para_elem = ParagraphElement()
        assert para_elem.get_text_content() == ""


class TestParagraph:
    """Test cases for Paragraph Pydantic dataclass."""

    def test_create_with_elements(self) -> None:
        """Test creating Paragraph with elements."""
        text_run = TextRun(content="Hello")
        para_elem = ParagraphElement(textRun=text_run)
        paragraph = Paragraph(elements=[para_elem])
        assert len(paragraph.elements) == 1
        assert paragraph.elements[0] == para_elem

    def test_create_with_default(self) -> None:
        """Test creating Paragraph with default empty elements."""
        paragraph = Paragraph()
        assert paragraph.elements == []

    def test_get_text_content_single_element(self) -> None:
        """Test getting text content from single element."""
        text_run = TextRun(content="Hello World")
        para_elem = ParagraphElement(textRun=text_run)
        paragraph = Paragraph(elements=[para_elem])
        assert paragraph.get_text_content() == "Hello World"

    def test_get_text_content_multiple_elements(self) -> None:
        """Test getting text content from multiple elements."""
        text_run1 = TextRun(content="Hello")
        text_run2 = TextRun(content="World")
        para_elem1 = ParagraphElement(textRun=text_run1)
        para_elem2 = ParagraphElement(textRun=text_run2)
        paragraph = Paragraph(elements=[para_elem1, para_elem2])
        assert paragraph.get_text_content() == "Hello World"

    def test_get_text_content_empty_elements(self) -> None:
        """Test getting text content from empty elements."""
        paragraph = Paragraph(elements=[])
        assert paragraph.get_text_content() == ""

    def test_get_text_content_mixed_elements(self) -> None:
        """Test getting text content from mix of empty and filled elements."""
        text_run = TextRun(content="Hello")
        para_elem1 = ParagraphElement(textRun=text_run)
        para_elem2 = ParagraphElement()  # No textRun
        paragraph = Paragraph(elements=[para_elem1, para_elem2])
        assert paragraph.get_text_content() == "Hello"


class TestContentElement:
    """Test cases for ContentElement Pydantic dataclass."""

    def test_create_with_paragraph(self) -> None:
        """Test creating ContentElement with paragraph."""
        text_run = TextRun(content="Hello")
        para_elem = ParagraphElement(textRun=text_run)
        paragraph = Paragraph(elements=[para_elem])
        content_elem = ContentElement(paragraph=paragraph)
        assert content_elem.paragraph == paragraph

    def test_create_with_default(self) -> None:
        """Test creating ContentElement with default None paragraph."""
        content_elem = ContentElement()
        assert content_elem.paragraph is None

    def test_get_text_content_with_paragraph(self) -> None:
        """Test getting text content when paragraph exists."""
        text_run = TextRun(content="Hello World")
        para_elem = ParagraphElement(textRun=text_run)
        paragraph = Paragraph(elements=[para_elem])
        content_elem = ContentElement(paragraph=paragraph)
        assert content_elem.get_text_content() == "Hello World"

    def test_get_text_content_without_paragraph(self) -> None:
        """Test getting text content when paragraph is None."""
        content_elem = ContentElement()
        assert content_elem.get_text_content() == ""


class TestGoogleDocsDeserializer:
    """Test cases for GoogleDocsDeserializer class."""

    def test_deserialize_text_run_with_content(self) -> None:
        """Test deserializing TextRun with content."""
        data: TextRunData = {"content": "Hello World"}
        text_run = GoogleDocsDeserializer.deserialize_text_run(data)
        assert isinstance(text_run, TextRun)
        assert text_run.content == "Hello World"

    def test_deserialize_text_run_without_content(self) -> None:
        """Test deserializing TextRun without content."""
        data: TextRunData = {}
        text_run = GoogleDocsDeserializer.deserialize_text_run(data)
        assert isinstance(text_run, TextRun)
        assert text_run.content == ""

    def test_deserialize_paragraph_element_with_text_run(self) -> None:
        """Test deserializing ParagraphElement with textRun."""
        data: ParagraphElementData = {"textRun": {"content": "Hello"}}
        para_elem = GoogleDocsDeserializer.deserialize_paragraph_element(data)
        assert isinstance(para_elem, ParagraphElement)
        assert para_elem.textRun is not None
        assert para_elem.textRun.content == "Hello"

    def test_deserialize_paragraph_element_without_text_run(self) -> None:
        """Test deserializing ParagraphElement without textRun."""
        data: ParagraphElementData = {}
        para_elem = GoogleDocsDeserializer.deserialize_paragraph_element(data)
        assert isinstance(para_elem, ParagraphElement)
        assert para_elem.textRun is None

    def test_deserialize_paragraph_with_elements(self) -> None:
        """Test deserializing Paragraph with elements."""
        data: ParagraphData = {
            "elements": [
                {"textRun": {"content": "Hello"}},
                {"textRun": {"content": "World"}},
            ]
        }
        paragraph = GoogleDocsDeserializer.deserialize_paragraph(data)
        assert isinstance(paragraph, Paragraph)
        assert len(paragraph.elements) == 2
        assert paragraph.elements[0].textRun is not None
        assert paragraph.elements[0].textRun.content == "Hello"
        assert paragraph.elements[1].textRun is not None
        assert paragraph.elements[1].textRun.content == "World"

    def test_deserialize_paragraph_without_elements(self) -> None:
        """Test deserializing Paragraph without elements."""
        data: ParagraphData = {}
        paragraph = GoogleDocsDeserializer.deserialize_paragraph(data)
        assert isinstance(paragraph, Paragraph)
        assert paragraph.elements == []

    def test_deserialize_content_element_with_paragraph(self) -> None:
        """Test deserializing ContentElement with paragraph."""
        data: ContentElementData = {
            "paragraph": {"elements": [{"textRun": {"content": "Hello World"}}]}
        }
        content_elem = GoogleDocsDeserializer.deserialize_content_element(data)
        assert isinstance(content_elem, ContentElement)
        assert content_elem.paragraph is not None
        assert content_elem.get_text_content() == "Hello World"

    def test_deserialize_content_element_without_paragraph(self) -> None:
        """Test deserializing ContentElement without paragraph."""
        data: ContentElementData = {}
        content_elem = GoogleDocsDeserializer.deserialize_content_element(data)
        assert isinstance(content_elem, ContentElement)
        assert content_elem.paragraph is None

    def test_deserialize_content_full_example(self) -> None:
        """Test deserializing complete content structure."""
        raw_content = cast(
            list[ContentElementData],
            [
                {
                    "paragraph": {
                        "elements": [
                            {"textRun": {"content": "First paragraph "}},
                            {"textRun": {"content": "with multiple runs."}},
                        ]
                    }
                },
                {
                    "paragraph": {
                        "elements": [{"textRun": {"content": "Second paragraph here."}}]
                    }
                },
            ],
        )

        content_elements = GoogleDocsDeserializer.deserialize_content(raw_content)
        assert len(content_elements) == 2
        assert (
            content_elements[0].get_text_content()
            == "First paragraph  with multiple runs."
        )
        assert content_elements[1].get_text_content() == "Second paragraph here."


class TestEdgeCases:
    """Test edge cases and error handling."""

    def test_edge_case_empty_content(self) -> None:
        """Test handling of empty content structures."""
        edge_cases = cast(
            list[ContentElementData],
            [
                {"paragraph": {"elements": [{"textRun": {}}]}},  # Empty textRun
                {"paragraph": {"elements": []}},  # Empty elements
                {"paragraph": {}},  # No elements
                {},  # Empty content element
                {"someOtherElement": {"data": "ignored"}},  # Unknown structure
            ],
        )

        content_elements = GoogleDocsDeserializer.deserialize_content(edge_cases)
        assert len(content_elements) == 5

        # All should have empty text content
        for element in content_elements:
            assert element.get_text_content() == ""

    def test_edge_case_mixed_content_types(self) -> None:
        """Test handling of mixed content types in textRun."""
        data = cast(
            ContentElementData,
            {
                "paragraph": {
                    "elements": [
                        {"textRun": {"content": 42}}  # Integer content  # type: ignore
                    ]
                }
            },
        )

        content_elem = GoogleDocsDeserializer.deserialize_content_element(data)
        assert content_elem.get_text_content() == "42"


class TestIntegrationWithGoogleClient:
    """Integration tests with GoogleClient."""

    def test_parse_content_integration(self) -> None:
        """Test integration with GoogleClient parse_content method."""
        from neia.google_client import GoogleClient

        # Create test content using deserializer
        sample_data = cast(
            list[ContentElementData],
            [
                {
                    "paragraph": {
                        "elements": [
                            {"textRun": {"content": "Hello "}},
                            {"textRun": {"content": "World!"}},
                        ]
                    }
                },
                {
                    "paragraph": {
                        "elements": [{"textRun": {"content": "Second paragraph."}}]
                    }
                },
            ],
        )

        content_elements = GoogleDocsDeserializer.deserialize_content(sample_data)

        # Test with GoogleClient
        client = GoogleClient("dummy_token.json")
        result = client.parse_content(content_elements)

        assert result == "Hello  World! Second paragraph."

    def test_empty_content_integration(self) -> None:
        """Test integration with empty content."""
        from neia.google_client import GoogleClient

        content_elements = GoogleDocsDeserializer.deserialize_content([])
        client = GoogleClient("dummy_token.json")
        result = client.parse_content(content_elements)

        assert result == ""


if __name__ == "__main__":
    # Allow running tests directly
    pytest.main([__file__])
