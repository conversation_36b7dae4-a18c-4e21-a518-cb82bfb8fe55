import csv
import json
import re
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Optional, cast

import click
import gitlab
from gitlab.v4.objects import ProjectMergeRequest
from gitlab.v4.objects.users import CurrentUser
from InquirerPy import inquirer
from InquirerPy.base.control import Choice
from rich import box
from rich.console import Console
from rich.table import Table
from rich.text import Text

from neia.helpers import resume_mr_discussion
from neia.logging.neia_logging_adapter import get_logger
from neia.models import IssueComment, NeiaIssue

logger = get_logger("GitLab")


@dataclass
class NeiaProjectInfo:
    id: str
    name: str
    git_url: str
    web_url: str
    description: Optional[str] = None


class GitLabClient:
    def __init__(self, url: str, token: str, project_id: str):
        self.gl = gitlab.Gitlab(url, private_token=token)
        self.project_id = project_id

    def get_user(self) -> Optional[CurrentUser]:
        self.gl.auth()
        return self.gl.user

    def assign_issue(self, project_id: str, issue_id: str) -> None:
        project = self.gl.projects.get(project_id)
        issue = project.issues.get(issue_id)
        user = self.get_user()
        logger.debug(f"Assigning issue {issue_id} to {user} in GitLab")
        if user:
            issue.assignee_ids = [user.attributes["id"]]
        issue.save()

    def get_issue(self, project_id: str, issue_id: str) -> NeiaIssue:
        project = self.gl.projects.get(project_id)
        issue = project.issues.get(issue_id)
        comments = [
            IssueComment(id=note.id, author=note.author["name"], body=note.body)
            for note in issue.notes.list()
            if not note.system
        ]
        return NeiaIssue(
            id=issue.iid,
            source="gitlab",
            title=issue.title,
            description=issue.description,
            comments=comments,
        )

    def get_project(self, project_id: str) -> NeiaProjectInfo:
        """Get project details from GitLab."""
        project = self.gl.projects.get(project_id)
        return NeiaProjectInfo(
            id=project.id,
            name=project.name,
            git_url=project.ssh_url_to_repo,
            web_url=project.web_url,
            description=project.description,
        )

    def create_branch(self, project_id: str, branch_name: str, ref: str) -> None:
        project = self.gl.projects.get(project_id)
        project.branches.create({"branch": branch_name, "ref": ref})

    def get_merge_request(
        self, *, mr_id: Optional[str] = None, mr_iid: Optional[str] = None
    ) -> Optional[ProjectMergeRequest]:
        if not mr_id and not mr_iid:
            logger.error("get_merge_request: MR ID or IID is required")
            raise Exception("MR ID or IID is required")
        user = self.get_user()
        if not user:
            logger.error("get_merge_request: User not found")
            raise Exception("User not found")
        mrs = self.gl.mergerequests.list(
            assignee_id=user.attributes["id"], get_all=True
        )
        mr = next((m for m in mrs if str(m.id) == mr_id or str(m.iid) == mr_iid), None)
        if mr:
            project = self.gl.projects.get(mr.project_id)
            return project.mergerequests.get(mr.iid)
        return None

    def get_merge_request_by_branch(
        self, project_id: str, source_branch: str
    ) -> Optional[ProjectMergeRequest]:
        project = self.gl.projects.get(project_id)
        mrs = project.mergerequests.list(
            state="opened", source_branch=source_branch, get_all=True
        )
        if mrs:
            mrs_list = list(mrs)
            if len(mrs_list) > 0:
                return cast(ProjectMergeRequest, mrs_list[0])
        return None

    def print_merge_requests_stats(self, export_format: Optional[str]) -> None:
        table_todo = Table(
            show_header=True, header_style="bold magenta", box=box.SQUARE
        )
        for column in ["ID", "IID", "Projet", "Titre", "CI", "Pending comments", "Âge"]:
            table_todo.add_column(column)

        table_pending = Table(
            show_header=True, header_style="bold magenta", box=box.SQUARE
        )
        for column in ["ID", "IID", "Projet", "Titre", "Âge"]:
            table_pending.add_column(column)

        table_closed = Table(
            show_header=True, header_style="bold magenta", box=box.SQUARE
        )
        for column in ["ID", "Projet", "Titre", "Résultat", "Date"]:
            table_closed.add_column(column)

        choices_todo = []
        choices_all = []

        export_data = []
        processing_times = []
        closed_as_merged = 0
        closed_as_refused = 0

        user = self.get_user()
        if not user:
            logger.debug("Error: Unable to authenticate with GitLab")
            return
        mrs = self.gl.mergerequests.list(assignee_id=user.id)

        # Check if user has any merge requests
        if not mrs:
            console = Console(force_terminal=True, width=160)
            console.print(
                f'\nNEIA Merge Requests Dashboard - {datetime.today().strftime("%d/%m/%Y")}'
            )
            console.print("\n📋 Aucune merge request assignée trouvée.")
            console.print("Vous n'avez actuellement aucune merge request assignée.")
            return

        # Add rows
        for mr_data in mrs:
            project = self.gl.projects.get(mr_data.project_id)
            mr = project.mergerequests.get(mr_data.iid)

            created_at = datetime.fromisoformat(mr.created_at)
            pipeline_error = self.get_pipeline_error(mr)

            resumed_discussions = []
            for d in mr.discussions.list():
                resume = resume_mr_discussion(mr, d)
                if resume is not None:
                    resumed_discussions.append(resume)

            if mr.state == "opened":
                now = datetime.now(timezone.utc)
                age = now - created_at
                if resumed_discussions or pipeline_error:
                    table_todo.add_row(
                        f"#{mr.id}",
                        f"{mr.iid}",
                        project.path,
                        Text(mr.title),
                        (
                            Text("KO", style="bold red")
                            if pipeline_error
                            else Text("OK", style="bold green")
                        ),
                        (
                            Text("OUI", style="bold red")
                            if resumed_discussions
                            else Text("NON", style="bold green")
                        ),
                        f"{age.days}j",
                    )
                    choices_todo.append(Choice(f"{mr.id}", name=f"#{mr.id} {mr.title}"))
                else:
                    table_pending.add_row(
                        f"#{mr.id}",
                        f"{mr.iid}",
                        project.path,
                        Text(mr.title),
                        f"{age.days}j",
                    )
            else:
                closed_at = datetime.fromisoformat(mr.merged_at or mr.closed_at)
                processing_times.append(closed_at - created_at)
                if mr.merged_at:
                    closed_as_merged += 1
                else:
                    closed_as_refused += 1
                table_closed.add_row(
                    f"#{mr.id}",
                    project.path,
                    Text(mr.title),
                    (
                        Text("Fusionnée", style="bold green")
                        if mr.merged_at
                        else Text("Rejetée", style="bold red")
                    ),
                    f'{closed_at.strftime("%d/%m/%Y")}',
                )

            export_data.append(
                {
                    "id": mr.id,
                    "internal_id": mr.iid,
                    "project": project.path,
                    "branch": mr.source_branch,
                    "title": mr.title,
                    "status": mr.state,
                    "ci_status": "OK" if pipeline_error else "KO",
                    "pending_comments": "OUI" if resumed_discussions else "NON",
                }
            )
            choices_all.append(Choice(f"{mr.id}", name=f"#{mr.id} {mr.title}"))

        closed = closed_as_merged + closed_as_refused
        success_rate = round(100 * closed_as_merged / closed) if closed else None
        processing_time = (
            sum(processing_times, timedelta()) / len(processing_times)
            if len(processing_times) > 0
            else None
        )

        console = Console(force_terminal=True, width=160)
        console.print(
            f'\nNEIA Merge Requests Dashboard - {datetime.today().strftime("%d/%m/%Y")}'
        )
        console.print("\n📊 MÉTRIQUES GLOBALES")
        console.print(
            f"Total MRs : {len(mrs)} | | À traiter : {len(table_todo.rows)} | En attente : {len(table_pending.rows)} | Traitées : {len(table_closed.rows)}"
        )
        if processing_time is not None:
            console.print(
                f"Temps moyen de traitement : {round(processing_time.total_seconds() / 86400, 1)} jours"
            )
        if success_rate is not None:
            console.print(f"Taux de succès : {success_rate}%")
        console.print(f"\n🔄 MRs À TRAITER PAR NEIA ({len(table_todo.rows)})")
        console.print(table_todo)
        console.print(
            f"\n🕒 MRs EN ATTENTE DE FEEDBACK HUMAIN ({len(table_pending.rows)})"
        )
        console.print(table_pending)
        console.print(f"\n✅ MRs TRAITÉES RÉCEMMENT ({len(table_closed.rows)})")
        console.print(table_closed)

        if export_format:
            if export_data:
                filename = f"export_mrs.{export_format}"
                with open(filename, "w") as f:
                    if export_format == "json":
                        json.dump(export_data, f, ensure_ascii=False, indent=2)
                    elif export_format == "csv":
                        writer = csv.DictWriter(f, fieldnames=export_data[0].keys())
                        writer.writeheader()
                        writer.writerows(export_data)
                console.print(f"\nThe MRs were exported to file {filename}.\n")
            else:
                console.print("\nNothing to export.")
        else:
            console.print(
                '\nTo export the MRs as JSON or CSV, use the "--export" option.'
            )

        console.print(
            'To get more details about a MR, use: "poetry run neia show-mr <ID>"'
        )
        console.print(
            'To process an existing MR, use: "poetry run neia process-mr <ID>"'
        )
        console.print('To review an existing MR, use: "poetry run neia review <IID>"')

        while True:
            console.print("\n\n")

            action = inquirer.select(
                message="Select an action:",
                choices=[
                    Choice("show-mr", name="Get more details about a MR"),
                    Choice("process-mr", name="Process an existing MR"),
                    Choice(
                        "review-mr",
                        name="Review an existing MR (automated code review)",
                    ),
                    Choice("exit", name="Exit"),
                ],
            ).execute()

            if action == "exit":
                break

            selected_mr = inquirer.select(
                message="Select a MR:",
                choices=choices_todo if action == "process-mr" else choices_all,
            ).execute()

            if action == "show-mr":
                self.print_merge_request_details(selected_mr)

            if action == "process-mr":
                mode = inquirer.select(
                    message="Select the level:",
                    choices=[
                        Choice(
                            "prompt-only",
                            name="Show the prompt without sending it to LLM",
                        ),
                        Choice("dry-run", name="Show changes without submitting them"),
                        Choice("process", name="Submit the changes"),
                    ],
                ).execute()
                prompt_only = mode == "prompt-only"
                dry_run = mode == "dry-run"
                self.process_merge_request(dry_run, prompt_only, mr_id=selected_mr)

            if action == "review-mr":
                import os

                from neia.code_reviewer import CodeReviewer

                review_mode = inquirer.select(
                    message="Select review mode:",
                    choices=[
                        Choice("dry-run", name="Analyze without posting to GitLab"),
                        Choice("post-review", name="Analyze and post review to GitLab"),
                    ],
                ).execute()

                dry_run_review = review_mode == "dry-run"

                llm_model = os.getenv("LLM_MODEL", "gpt-4o-mini")
                code_reviewer = CodeReviewer(self, self.project_id, llm_model)
                code_reviewer.review(mr_id=selected_mr, dry_run=dry_run_review)

    def print_merge_request_details(self, mr_id: str) -> None:
        console = Console(force_terminal=True, width=160)
        mr = self.get_merge_request(mr_id=mr_id)
        if not mr:
            console.print("The MR was not found.")
            return
        project = self.gl.projects.get(mr.project_id)
        created_at = datetime.fromisoformat(mr.created_at)
        console.print("\nMerge Request Details\n")
        console.print(f"ID: {mr.id}")
        console.print(f"Internal ID: {mr.iid}")
        console.print(f'Created at: {created_at.strftime("%d/%m/%Y")}')
        console.print(f"\nTitle: {mr.title}")
        console.print(f"URL: {mr.web_url}")
        console.print(f"Project: {project.name} ({project.path})")
        console.print(f"Source Branch: {mr.source_branch}")
        console.print(f"Target Branch: {mr.target_branch}")
        console.print(f"\nStatus: {mr.state}")
        console.print(f"Merge Status: {mr.merge_status} ({mr.detailed_merge_status})")
        console.print(f'Draft: {"YES" if mr.draft else "NO"}')

    def get_pipeline_error(self, mr: ProjectMergeRequest) -> Optional[str]:
        project = self.gl.projects.get(mr.project_id)
        pipelines = mr.pipelines.list(get_all=True)
        if pipelines:
            pipelines_list = list(pipelines)
            if len(pipelines_list) > 0:
                pipeline = project.pipelines.get(pipelines_list[0].id)
                if pipeline.status == "failed":
                    jobs = pipeline.jobs.list()
                    failed_jobs_ids = [j.id for j in jobs if j.status == "failed"]
                    for failed_job_id in failed_jobs_ids:
                        job = project.jobs.get(failed_job_id)  # ensure full job object
                        title = f'Job #{job.id} "{job.name}" ({job.ref}) failed:\n\n'
                        try:
                            trace_data = job.trace()
                            log = str(trace_data)
                        except Exception:
                            log = "Unable to retrieve job log"
                        pattern = re.compile(
                            r"^=*\s*(FAILURES|ERRORS)\s*=*$", re.MULTILINE
                        )
                        match = pattern.search(log)
                        if match:
                            start_index = match.end()
                            detail = log[start_index:].strip()
                        else:
                            detail = log
                        return title + detail
        return None

    def submit_review_changes(
        self,
        project_id: str,
        discussion_id: Optional[str],
        commit_id: Optional[str],
        source_branch: str,
        title: str,
        description: str,
    ) -> str:
        # Check if MR already exists for this branch
        mr = self.get_merge_request_by_branch(project_id, source_branch)
        if mr:
            # Add new changes as comment
            content = {"body": f"Commit: {commit_id}\n\n{description}"}
            if discussion_id:
                mr.discussions.get(discussion_id).notes.create(content)
            else:
                mr.notes.create(content)
            return mr.web_url
        else:
            # Create new MR
            project = self.gl.projects.get(project_id)
            user = self.get_user()
            mr_data = {
                "source_branch": source_branch,
                "target_branch": "main",
                "title": title,
                "description": description,
            }
            if user and hasattr(user, "id"):
                mr_data["assignee_id"] = user.id
            new_mr = project.mergerequests.create(mr_data)
            return new_mr.attributes["web_url"]

    def process_merge_request(
        self,
        dry_run: bool,
        prompt_only: bool,
        bypass_action_plan: bool = False,
        *,
        mr_id: Optional[str] = None,
        mr_iid: Optional[str] = None,
    ) -> None:
        from neia.issue_loader import get_issue_from_url
        from neia.repository_analyzer import analyze

        mr = self.get_merge_request(mr_id=mr_id, mr_iid=mr_iid)
        if not mr:
            click.echo("The MR was not found.")
            return
        if mr.state != "opened":
            click.echo("The MR is not opened.")
            return
        project_id = mr.project_id
        branch_name = mr.source_branch

        try:
            issue = get_issue_from_url(mr.description, project_id)
        except Exception as e:
            click.echo(str(e))
            return

        analyze(
            self,
            project_id,
            branch_name,
            issue,
            mr,
            dry_run,
            prompt_only,
            bypass_action_plan,
        )
