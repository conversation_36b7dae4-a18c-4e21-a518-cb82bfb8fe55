.PHONY: release-major release-minor release-patch lint lint-fix help

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

# Get current version from pyproject.toml
CURRENT_VERSION := $(shell grep '^version = ' pyproject.toml | sed 's/version = "\(.*\)"/\1/')

help: ## Show this help message
	@echo "NEIA Development & Release Management"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint           - Run all code quality checks (same as CI)"
	@echo "  lint-fix       - Auto-fix formatting and linting issues"
	@echo ""
	@echo "Release Management:"
	@echo "  release-major  - Create a major release (x.0.0)"
	@echo "  release-minor  - Create a minor release (x.y.0)"
	@echo "  release-patch  - Create a patch release (x.y.z)"
	@echo ""
	@echo "Current version: $(CURRENT_VERSION)"

lint: ## Run all code quality checks (same as CI)
	@echo "$(BLUE)🔍 Running code quality checks (same as CI)...$(NC)"
	@./lint.sh

lint-fix: ## Auto-fix formatting and linting issues
	@echo "$(BLUE)🔧 Auto-fixing formatting and linting issues...$(NC)"
	@./lint.sh --fix

test: ## Run all tests (usage: make test [file/pattern])
	@echo "$(BLUE)🏃 Running tests...$(NC)"
	@poetry run pytest $(filter-out $@,$(MAKECMDGOALS)) $(ARGS)

test-coverage: ## Run all tests with coverage
	@echo "$(BLUE)🏃 Running tests with coverage...$(NC)"
	@poetry run coverage run -m pytest
	@poetry run coverage xml -o cov.xml
	@poetry run coverage report -m

# Handle additional arguments as targets to avoid "No rule to make target" errors
%:
	@:

release-major: ## Create a major release (x.0.0)
	@echo "$(BLUE)Creating major release...$(NC)"
	@echo "$(YELLOW)Current version: $(CURRENT_VERSION)$(NC)"
	$(eval NEW_VERSION := $(shell python3 -c "v='$(CURRENT_VERSION)'.split('.'); print(f'{int(v[0])+1}.0.0')"))
	@echo "$(GREEN)New version: $(NEW_VERSION)$(NC)"
	@$(MAKE) _do_release VERSION=$(NEW_VERSION)

release-minor: ## Create a minor release (x.y.0)
	@echo "$(BLUE)Creating minor release...$(NC)"
	@echo "$(YELLOW)Current version: $(CURRENT_VERSION)$(NC)"
	$(eval NEW_VERSION := $(shell python3 -c "v='$(CURRENT_VERSION)'.split('.'); print(f'{v[0]}.{int(v[1])+1}.0')"))
	@echo "$(GREEN)New version: $(NEW_VERSION)$(NC)"
	@$(MAKE) _do_release VERSION=$(NEW_VERSION)

release-patch: ## Create a patch release (x.y.z)
	@echo "$(BLUE)Creating patch release...$(NC)"
	@echo "$(YELLOW)Current version: $(CURRENT_VERSION)$(NC)"
	$(eval NEW_VERSION := $(shell python3 -c "v='$(CURRENT_VERSION)'.split('.'); print(f'{v[0]}.{v[1]}.{int(v[2])+1}')"))
	@echo "$(GREEN)New version: $(NEW_VERSION)$(NC)"
	@$(MAKE) _do_release VERSION=$(NEW_VERSION)

_do_release:
	@echo "$(BLUE)Checking out main branch and pulling latest changes...$(NC)"
	git checkout main
	git pull
	@echo "$(BLUE)Updating version in pyproject.toml...$(NC)"
	sed -i 's/version = ".*"/version = "$(VERSION)"/' pyproject.toml
	@echo "$(BLUE)Committing version update...$(NC)"
	git add pyproject.toml
	git commit -m "chore: Release version v$(VERSION)"
	@echo "$(BLUE)Creating git tag v$(VERSION)...$(NC)"
	git tag -a v$(VERSION) -m "Version $(VERSION)"
	@echo "$(BLUE)Pushing changes and tags...$(NC)"
	git push --follow-tags origin main
	@echo "$(GREEN)✅ Release v$(VERSION) created successfully!$(NC)"
	@echo "$(YELLOW)Don't forget to create a release on GitLab with the changelog.$(NC)"

# Default target
all: help 