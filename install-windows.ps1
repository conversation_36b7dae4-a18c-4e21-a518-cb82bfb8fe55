#!/usr/bin/env pwsh

# NEIA Installation Script for Windows
# This script sets up NEIA for local use with Docker on Windows

param(
    [string]$Version = $env:NEIA_VERSION,
    [switch]$Help
)

$ErrorActionPreference = "Stop"

# Configuration
$NEIA_CONFIG_DIR = Join-Path $env:USERPROFILE ".neia"
$NEIA_IMAGE_REGISTRY = "gitlab.lundimatin.app:5050/artificial-intelligence-ia/neia"
$NEIA_VERSION = if ($Version) { $Version } else { "latest" }
$NEIA_IMAGE = "${NEIA_IMAGE_REGISTRY}:${NEIA_VERSION}"
$NEIA_WRAPPER_SCRIPT = Join-Path $env:USERPROFILE ".local\bin\neia.ps1"
$NEIA_WRAPPER_BAT = Join-Path $env:USERPROFILE ".local\bin\neia.bat"

# Colors for output
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Show usage information
function Show-Usage {
    Write-Host "NEIA Installation Script for Windows"
    Write-Host ""
    Write-Host "Usage: .\install.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -Version VERSION    Install specific version (default: latest)"
    Write-Host "  -Help              Show this help message"
    Write-Host ""
    Write-Host "Environment variables:"
    Write-Host "  NEIA_VERSION       Version to install (default: latest)"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\install.ps1                    # Install latest version"
    Write-Host "  .\install.ps1 -Version v1.0.0   # Install version v1.0.0"
    Write-Host "  `$env:NEIA_VERSION='v1.0.0'; .\install.ps1  # Install version v1.0.0"
}

# Check if Docker is installed and running
function Test-Docker {
    Write-Info "Checking Docker installation..."
    
    if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker is not installed. Please install Docker Desktop for Windows first."
        Write-Host "Download from: https://www.docker.com/products/docker-desktop/"
        exit 1
    }
    
    try {
        docker info | Out-Null
        Write-Success "Docker is running"
    }
    catch {
        Write-Error "Docker is not running. Please start Docker Desktop first."
        exit 1
    }
}

# Create NEIA configuration directory structure
function Initialize-ConfigDir {
    Write-Info "Setting up NEIA configuration directory..."
    
    # Create directories
    New-Item -ItemType Directory -Force -Path $NEIA_CONFIG_DIR | Out-Null
    New-Item -ItemType Directory -Force -Path (Join-Path $NEIA_CONFIG_DIR "projects") | Out-Null
    New-Item -ItemType Directory -Force -Path (Join-Path $NEIA_CONFIG_DIR "cache") | Out-Null
    
    # Create default configuration file if it doesn't exist
    $configFile = Join-Path $NEIA_CONFIG_DIR "config.env"
    if (!(Test-Path $configFile)) {
        Write-Info "Creating default configuration file..."
        
        $configContent = @'
# NEIA Configuration File
# Copy this file and update with your credentials

# GitLab Configuration
GITLAB_URL=https://********************.com
GITLAB_TOKEN=your-personal-access-token
GITLAB_PROJECT_ID=your-project-id

# Tuleap Configuration (optional)
TULEAP_API_URL=https://your-tuleap-instance.com
TULEAP_USER=your-tuleap-user
TULEAP_PASSWORD=your-tuleap-password

# LLM API Keys (choose one)
OPENAI_API_KEY=your-openai-api-key

# Default LLM Model (see https://aider.chat/docs/llms.html)
LLM_MODEL=gpt-4o-mini
'@
        
        Set-Content -Path $configFile -Value $configContent
        Write-Warning "Please edit $configFile with your credentials before using NEIA."
    }
    else {
        Write-Info "Configuration file already exists at $configFile"
    }
    
    # Store the version information
    $versionFile = Join-Path $NEIA_CONFIG_DIR ".version"
    $versionContent = @"
NEIA_VERSION=$NEIA_VERSION
NEIA_IMAGE=$NEIA_IMAGE
"@
    Set-Content -Path $versionFile -Value $versionContent
    
    Write-Success "Configuration directory set up at $NEIA_CONFIG_DIR"
}

# Pull the latest NEIA Docker image
function Get-NeiaImage {
    Write-Info "Pulling NEIA Docker image: $NEIA_IMAGE"
    
    try {
        docker pull $NEIA_IMAGE
        Write-Success "NEIA Docker image pulled successfully"
    }
    catch {
        Write-Error "Failed to pull NEIA Docker image: $NEIA_IMAGE"
        Write-Info "Available versions can be found at: https://gitlab.lundimatin.app/artificial-intelligence-ia/neia/container_registry"
        exit 1
    }
}

# Create the neia wrapper script
function New-WrapperScript {
    Write-Info "Creating NEIA wrapper scripts..."
    
    # Ensure .local/bin exists
    $binDir = Split-Path $NEIA_WRAPPER_SCRIPT -Parent
    New-Item -ItemType Directory -Force -Path $binDir | Out-Null
    
    # Create PowerShell wrapper script
    $wrapperContent = @'
#!/usr/bin/env pwsh

# NEIA Docker Wrapper Script for Windows
# This script provides an easy interface to run NEIA commands

param(
    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]]$Arguments
)

$NEIA_CONFIG_DIR = Join-Path $env:USERPROFILE ".neia"
$NEIA_IMAGE_REGISTRY = "gitlab.lundimatin.app:5050/artificial-intelligence-ia/neia"
$CONTAINER_NAME = "neia-instance"

# Load version information
$versionFile = Join-Path $NEIA_CONFIG_DIR ".version"
if (Test-Path $versionFile) {
    Get-Content $versionFile | ForEach-Object {
        if ($_ -match "^([^=]+)=(.+)$") {
            Set-Variable -Name $matches[1] -Value $matches[2] -Scope Script
        }
    }
}
else {
    $NEIA_VERSION = "latest"
    $NEIA_IMAGE = "${NEIA_IMAGE_REGISTRY}:latest"
}

# Function to run NEIA command
function Invoke-Neia {
    param([string[]]$NeiaArgs)
    
    # Stop any existing container
    docker stop $CONTAINER_NAME 2>$null
    docker rm $CONTAINER_NAME 2>$null
    
    # SSH agent forwarding for Windows (using pageant or ssh-agent)
    $sshArgs = @()
    $sshMountArgs = @()
    
    # Check for SSH agent (works with OpenSSH on Windows)
    if ($env:SSH_AUTH_SOCK) {
        # On Windows with WSL or Git Bash SSH agent
        $sshArgs += @("-v", "${env:SSH_AUTH_SOCK}:/ssh-agent", "-e", "SSH_AUTH_SOCK=/ssh-agent")
    }
    elseif (Get-Process ssh-agent -ErrorAction SilentlyContinue) {
        Write-Warning "SSH agent detected but SSH_AUTH_SOCK not set. SSH agent forwarding may not work properly."
    }
    else {
        Write-Warning "No SSH agent detected. Make sure your SSH agent is running: ssh-add -l"
    }
    
    # Mount SSH directory (Windows path)
    $sshDir = Join-Path $env:USERPROFILE ".ssh"
    if (Test-Path $sshDir) {
        $sshMountArgs += @("-v", "${sshDir}:/home/<USER>/.ssh:ro")
    }
    
    # Convert Windows paths to Unix-style for Docker
    $configDirUnix = $NEIA_CONFIG_DIR -replace '\\', '/' -replace '^([A-Z]):', '//$1'
    $projectsDirUnix = (Join-Path $NEIA_CONFIG_DIR "projects") -replace '\\', '/' -replace '^([A-Z]):', '//$1'
    
    # Build Docker run arguments
    $dockerArgs = @(
        "run", "--rm", "-it",
        "--name", $CONTAINER_NAME,
        "--env-file", (Join-Path $NEIA_CONFIG_DIR "config.env")
    )
    
    $dockerArgs += $sshArgs
    $dockerArgs += $sshMountArgs
    $dockerArgs += @(
        "-e", "HOME=/home/<USER>",
        "-v", "${configDirUnix}:/home/<USER>/.neia",
        "-v", "${projectsDirUnix}:/app/projects",
        "-w", "/app",
        $NEIA_IMAGE
    )
    $dockerArgs += $NeiaArgs
    
    # Run Docker command
    & docker @dockerArgs
}

# Function to update NEIA
function Update-Neia {
    param([string]$TargetVersion = "latest")
    
    $newImage = "${NEIA_IMAGE_REGISTRY}:${TargetVersion}"
    
    Write-Host "Updating NEIA to version: $TargetVersion"
    try {
        docker pull $newImage
        
        # Update version file
        $versionContent = @"
NEIA_VERSION=$TargetVersion
NEIA_IMAGE=$newImage
"@
        Set-Content -Path (Join-Path $NEIA_CONFIG_DIR ".version") -Value $versionContent
        Write-Host "NEIA updated successfully to version: $TargetVersion"
    }
    catch {
        Write-Host "Failed to pull NEIA version: $TargetVersion"
        Write-Host "Available versions can be found at: https://gitlab.lundimatin.app/artificial-intelligence-ia/neia/container_registry"
        exit 1
    }
}

# Handle special commands
switch ($Arguments[0]) {
    "update" {
        $version = if ($Arguments.Length -gt 1) { $Arguments[1] } else { "latest" }
        Update-Neia -TargetVersion $version
    }
    "version" {
        Write-Host "NEIA version: $NEIA_VERSION"
        Write-Host "Docker image: $NEIA_IMAGE"
        if (Get-Command docker -ErrorAction SilentlyContinue) {
            try {
                docker image inspect $NEIA_IMAGE | Out-Null
                Write-Host "Image status: Available locally"
            }
            catch {
                Write-Host "Image status: Not available locally (run 'neia update' to pull)"
            }
        }
    }
    "config" {
        Write-Host "Opening NEIA configuration file..."
        $configFile = Join-Path $NEIA_CONFIG_DIR "config.env"
        if ($env:EDITOR) {
            & $env:EDITOR $configFile
        }
        else {
            notepad $configFile
        }
    }
    "logs" {
        try {
            docker logs $CONTAINER_NAME
        }
        catch {
            Write-Host "No running NEIA container found"
        }
    }
    "status" {
        Write-Host "NEIA version: $NEIA_VERSION"
        $container = docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}" | Select-String $CONTAINER_NAME
        if ($container) {
            Write-Host "NEIA container is running"
            docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        }
        else {
            Write-Host "NEIA container is not running"
        }
    }
    "clean-cache" {
        Write-Host "Cleaning NEIA cache..."
        $projectsDir = Join-Path $NEIA_CONFIG_DIR "projects"
        if (Test-Path $projectsDir) {
            Write-Host "Removing projects directory: $projectsDir"
            Remove-Item -Path $projectsDir -Recurse -Force
            New-Item -ItemType Directory -Force -Path $projectsDir | Out-Null
            Write-Host "Cache cleaned successfully!"
        }
        else {
            Write-Host "Projects directory not found: $projectsDir"
        }
    }
    "clean-images" {
        Write-Host "Cleaning NEIA Docker images..."
        
        # Stop any running container first
        docker stop $CONTAINER_NAME 2>$null
        docker rm $CONTAINER_NAME 2>$null
        
        # Remove all NEIA Docker images
        try {
            $neiaImages = docker images --format "{{.Repository}}:{{.Tag}}" | Select-String "^$NEIA_IMAGE_REGISTRY:"
            if ($neiaImages) {
                Write-Host "Found NEIA images:"
                $neiaImages | ForEach-Object { Write-Host $_ }
                $imageList = $neiaImages -join " "
                Invoke-Expression "docker rmi $imageList" 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "NEIA Docker images cleaned successfully!"
                }
                else {
                    Write-Host "Warning: Some NEIA images could not be removed"
                }
            }
            else {
                Write-Host "No NEIA Docker images found locally"
            }
        }
        catch {
            Write-Host "No NEIA Docker images found locally"
        }
    }
    "uninstall" {
        Write-Host "NEIA Uninstaller"
        Write-Host "================"
        Write-Host ""
        
        # Stop and remove any running container
        Write-Host "Stopping and removing NEIA container..."
        docker stop $CONTAINER_NAME 2>$null
        docker rm $CONTAINER_NAME 2>$null
        
        # Remove all NEIA Docker images
        Write-Host "Removing all NEIA Docker images..."
        try {
            $neiaImages = docker images --format "{{.Repository}}:{{.Tag}}" | Select-String "^$NEIA_IMAGE_REGISTRY:"
            if ($neiaImages) {
                Write-Host "Found NEIA images:"
                $neiaImages | ForEach-Object { Write-Host $_ }
                $imageList = $neiaImages -join " "
                Invoke-Expression "docker rmi $imageList" 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "NEIA Docker images removed"
                }
                else {
                    Write-Host "Warning: Some NEIA images could not be removed"
                }
            }
            else {
                Write-Host "No NEIA Docker images found locally"
            }
        }
        catch {
            Write-Host "No NEIA Docker images found locally"
        }
        
        # Remove projects directory
        $projectsDir = Join-Path $NEIA_CONFIG_DIR "projects"
        if (Test-Path $projectsDir) {
            Write-Host "Removing projects directory: $projectsDir"
            Remove-Item -Path $projectsDir -Recurse -Force
        }
        
        # Ask about configuration removal
        Write-Host ""
        Write-Host "Do you want to remove the NEIA configuration directory ($NEIA_CONFIG_DIR)?"
        Write-Host "This will delete your config.env file."
        $response = Read-Host "Remove configuration? [y/N]"
        if ($response -match '^[Yy]') {
            Write-Host "Removing configuration directory: $NEIA_CONFIG_DIR"
            Remove-Item -Path $NEIA_CONFIG_DIR -Recurse -Force
        }
        else {
            Write-Host "Keeping configuration directory: $NEIA_CONFIG_DIR"
        }
        
        # Remove wrapper scripts
        Write-Host "Removing NEIA wrapper scripts..."
        $wrapperScript = Join-Path $env:USERPROFILE ".local\bin\neia.ps1"
        $wrapperBat = Join-Path $env:USERPROFILE ".local\bin\neia.bat"
        
        if (Test-Path $wrapperScript) {
            Remove-Item -Path $wrapperScript -Force
        }
        if (Test-Path $wrapperBat) {
            Remove-Item -Path $wrapperBat -Force
        }
        
        Write-Host ""
        Write-Host "NEIA has been uninstalled successfully!"
        Write-Host "Note: You may want to remove the PATH entry from your environment variables."
    }
    { $_ -in @("help-wrapper", "--help-wrapper") } {
        Write-Host "NEIA - AI-powered GitLab assistant"
        Write-Host ""
        Write-Host "Usage: neia <command> [options]"
        Write-Host ""
        Write-Host "Management commands:"
        Write-Host "  update [version]              Update NEIA to latest or specific version"
        Write-Host "  version                       Show current NEIA version"
        Write-Host "  config                        Edit NEIA configuration"
        Write-Host "  status                        Show NEIA container status"
        Write-Host "  logs                          Show NEIA container logs"
        Write-Host "  clean-cache                   Clean NEIA projects cache"
        Write-Host "  clean-images                  Remove all NEIA Docker images"
        Write-Host "  uninstall                     Uninstall NEIA completely"
        Write-Host ""
        Write-Host "Examples:"
        Write-Host "  neia update                   # Update to latest version"
        Write-Host "  neia update v1.0.0            # Update to specific version"
        Write-Host "  neia clean-cache              # Clean cache and projects"
        Write-Host "  neia clean-images             # Remove all Docker images"
        Write-Host "  neia uninstall                # Remove NEIA completely"
        Write-Host ""
        Write-Host "Current version: $NEIA_VERSION"
        Write-Host "Configuration file: $(Join-Path $NEIA_CONFIG_DIR 'config.env')"
    }
    default {
        Invoke-Neia -NeiaArgs $Arguments
    }
}
'@
    
    Set-Content -Path $NEIA_WRAPPER_SCRIPT -Value $wrapperContent
    
    # Create batch file wrapper for compatibility
    $batContent = @"
@echo off
powershell.exe -NoProfile -ExecutionPolicy Bypass -File "$NEIA_WRAPPER_SCRIPT" %*
"@
    
    Set-Content -Path $NEIA_WRAPPER_BAT -Value $batContent
    
    Write-Success "NEIA wrapper scripts created:"
    Write-Success "  PowerShell: $NEIA_WRAPPER_SCRIPT"
    Write-Success "  Batch: $NEIA_WRAPPER_BAT"
}

# Add to PATH if needed
function Add-ToPath {
    $binDir = Split-Path $NEIA_WRAPPER_SCRIPT -Parent
    
    # Get current user PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    
    # Check if the bin directory is already in PATH
    if ($currentPath -split ';' -notcontains $binDir) {
        Write-Info "Adding $binDir to user PATH..."
        
        $newPath = if ($currentPath) { "$currentPath;$binDir" } else { $binDir }
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        
        # Update current session PATH
        $env:PATH = "$env:PATH;$binDir"
        
        Write-Success "Added $binDir to PATH"
        Write-Info "You may need to restart your terminal for PATH changes to take effect"
    }
    else {
        Write-Info "$binDir is already in PATH"
    }
}

# Check PowerShell execution policy
function Test-ExecutionPolicy {
    $policy = Get-ExecutionPolicy -Scope CurrentUser
    if ($policy -eq "Restricted") {
        Write-Warning "PowerShell execution policy is Restricted. Setting to RemoteSigned for current user..."
        try {
            Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
            Write-Success "Execution policy updated to RemoteSigned"
        }
        catch {
            Write-Error "Failed to update execution policy. You may need to run as administrator or manually set execution policy."
            Write-Info "Run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser"
            exit 1
        }
    }
}

# Main installation process
function Install-Neia {
    if ($Help) {
        Show-Usage
        return
    }
    
    Write-Host "============================="
    Write-Host "NEIA Installation for Windows"
    Write-Host "============================="
    Write-Host ""
    Write-Info "Installing NEIA version: $NEIA_VERSION"
    Write-Host ""
    
    Test-ExecutionPolicy
    Test-Docker
    Initialize-ConfigDir
    Get-NeiaImage
    New-WrapperScript
    Add-ToPath
    
    Write-Host ""
    Write-Host "============================="
    Write-Success "NEIA installation completed!"
    Write-Host "============================="
    Write-Host ""
    Write-Info "Installed version: $NEIA_VERSION"
    Write-Info "Next steps:"
    Write-Host "1. Edit your configuration: neia config"
    Write-Host "2. Restart your terminal or refresh PATH"
    Write-Host "3. Test NEIA: neia --help"
    Write-Host ""
    Write-Info "Example usage:"
    Write-Host "  neia process-issue https://gitlab.com/owner/repo/-/issues/123"
    Write-Host "  neia list-mrs"
    Write-Host "  neia update        # Update to latest version"
    Write-Host "  neia version       # Check current version"
    Write-Host ""
    Write-Info "Available commands:"
    Write-Host "  PowerShell: neia <command>"
    Write-Host "  Command Prompt: neia.bat <command>"
}

# Run the installation
Install-Neia 