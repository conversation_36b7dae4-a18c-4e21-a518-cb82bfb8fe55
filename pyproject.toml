[tool.poetry]
name = "neia"
version = "0.1.0"
description = "GitLab automation tool for repository analysis and changes"
authors = ["<PERSON> MARTIN <<EMAIL>>", "<PERSON><PERSON> IVASKA <<EMAIL>>", "Gaëtan FLAMENT <<EMAIL>"]

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
python-gitlab = "^3.15.0"
click = "^8.1.3"
gitpython = "^3.1.31"
aider-chat = "^0.71.1"
rich = "13.9.4"
google-api-python-client = "^2.176.0"
google-auth-httplib2 = "^0.2.0"
google-auth-oauthlib = "^1.2.2"
python-dotenv = "^1.0.1"
inquirerpy = "^0.3.4"
pytest-cov = "^6.2.1"

[tool.poetry.group.dev.dependencies]
pytest = "^7.3.1"
black = "^23.7.0"
isort = "^5.12.0"
ruff = "^0.7.4"
mypy = "^1.11.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
neia = "neia.cli:run"

# Tool configurations
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | projects
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["neia"]
skip_glob = ["projects/**/*"]

[tool.ruff]
line-length = 88
target-version = "py39"
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]

# See https://mypy.readthedocs.io/en/stable/config_file.html
[tool.mypy]
python_version = "3.9"
exclude = [
    "^projects/.*",
    "^build/.*",
    "^dist/.*",
]

[[tool.mypy.overrides]]
module = [
    "gitlab.*",
    "git.*",
    "aider.*",
    "inquirerpy.*",
    "google_auth_oauthlib.*",
    "googleapiclient.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["neia"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "slow: Slow tests that may take longer to run"
]
# Exclude projects directory and other non-relevant paths
norecursedirs = [
    "projects",
    ".git",
    ".tox",
    "dist",
    "build",
    "*.egg",
    ".venv",
    "venv",
]
