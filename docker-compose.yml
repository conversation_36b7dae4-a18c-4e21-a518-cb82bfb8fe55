services:
  neia:
    image: registry.gitlab.lundimatin.app/artificial-intelligence-ia/neia:${NEIA_VERSION:-latest}
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      # Configuration will be loaded from ~/.neia/config
      - NEIA_CONFIG_DIR=/home/<USER>/.neia
      # SSH agent forwarding for Git operations
      - SSH_AUTH_SOCK=/ssh-agent
    volumes:
      # Mount user's NEIA configuration directory
      - ${HOME}/.neia:/${NEIA_CONFIG_DIR}
      # Mount user's projects directory for repository storage
      - ${HOME}/.neia/projects:/${NEIA_CONFIG_DIR}/projects
      # SSH agent forwarding for Git operations
      - ${SSH_AUTH_SOCK:-/dev/null}:/ssh-agent
      # Mount host SSH directory for known_hosts and keys
      - ${HOME}/.ssh:/home/<USER>/.ssh:ro
    # This will be overridden by the neia wrapper script
    command: ["--help"]
