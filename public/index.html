<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>NEIA - Assistant GitLab alimenté par l'IA</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Helvetica, Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
      }

      .header {
        text-align: center;
        color: white;
        margin-bottom: 3rem;
      }

      .header h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 1.2rem;
        opacity: 0.9;
      }

      .content {
        background: white;
        border-radius: 16px;
        padding: 3rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }

      .install-section {
        margin-bottom: 3rem;
      }

      .install-section h2 {
        color: #4a5568;
        margin-bottom: 1.5rem;
        font-size: 2rem;
        border-bottom: 3px solid #667eea;
        padding-bottom: 0.5rem;
      }

      .install-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .install-card {
        background: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 2rem;
        transition: all 0.3s ease;
        text-align: center;
      }

      .install-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
        border-color: #667eea;
      }

      .install-card .icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }

      .install-card h3 {
        color: #2d3748;
        margin-bottom: 1rem;
        font-size: 1.5rem;
      }

      .install-card p {
        color: #718096;
        margin-bottom: 2rem;
        line-height: 1.5;
      }

      .btn {
        display: inline-block;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 0.5rem;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
      }

      .btn-secondary {
        background: transparent;
        border: 2px solid #667eea;
        color: #667eea;
      }

      .btn-secondary:hover {
        background: #667eea;
        color: white;
      }

      .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 1.5rem;
        border-radius: 8px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        font-size: 0.9rem;
        overflow-x: auto;
        margin: 1rem 0;
      }

      .docs-section {
        background: linear-gradient(135deg, #ff9a8b 0%, #a8e6cf 100%);
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        color: white;
        margin-bottom: 2rem;
      }

      .docs-section h2 {
        margin-bottom: 1rem;
        border: none;
        color: white;
      }

      .footer {
        text-align: center;
        margin-top: 3rem;
        color: #718096;
      }

      .feature-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
      }

      .feature-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #f0f4f8;
        border-radius: 8px;
      }

      .feature-item span {
        margin-right: 0.5rem;
        font-size: 1.2rem;
      }

      @media (max-width: 768px) {
        .header h1 {
          font-size: 2rem;
        }

        .container {
          padding: 1rem;
        }

        .content {
          padding: 2rem;
        }

        .install-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🤖 NEIA</h1>
        <p>
          Assistant GitLab alimenté par l'IA pour les modifications automatisées
          de dépôts
        </p>
      </div>

      <div class="content">
        <div class="docs-section">
          <h2>📚 Documentation</h2>
          <p>
            Commencez à utiliser NEIA en lisant notre documentation complète
          </p>
          <a
            href="https://docs.google.com/document/d/103wvdJBJ8NJ1fG-dmA-amLy6MKEQFOhkdH_tCMc7cnU/edit?tab=t.0"
            class="btn"
            target="_blank"
          >
            📖 Voir la Documentation
          </a>
        </div>

        <div class="install-section">
          <h2>🚀 Installation Rapide</h2>
          <p>
            Choisissez votre système d'exploitation et faites fonctionner NEIA
            en quelques minutes :
          </p>

          <div class="install-grid">
            <div class="install-card">
              <div class="icon">🐧</div>
              <h3>Linux</h3>
              <p>
                Installateur Linux universel supportant toutes les distributions
                majeures avec intégration Docker.
              </p>
              <div class="code-block">
                curl -fsSL
                https://pages.lundimatin.app/artificial-intelligence-ia/neia/install-linux.sh
                | bash
              </div>
              <a href="install-linux.sh" class="btn" download
                >Télécharger le Script</a
              >
            </div>

            <div class="install-card">
              <h3>Windows</h3>
              <p>
                Installateur PowerShell avec support Docker Desktop pour
                Windows.
              </p>
              <div class="code-block">
                Invoke-WebRequest -Uri
                https://pages.lundimatin.app/artificial-intelligence-ia/neia/install-windows.ps1
                -OutFile install.ps1; .\install.ps1
              </div>
              <a href="install-windows.ps1" class="btn" download
                >Télécharger le Script</a
              >
            </div>

            <div class="install-card">
              <div class="icon">🍎</div>
              <h3>macOS</h3>
              <p>
                Installateur optimisé avec intégration Homebrew, support Docker
                Desktop, et intégration trousseau macOS.
              </p>
              <div class="code-block">
                curl -fsSL
                https://pages.lundimatin.app/artificial-intelligence-ia/neia/install-macos.sh
                | bash
              </div>
              <a href="install-macos.sh" class="btn" download
                >Télécharger le Script</a
              >
            </div>
          </div>
        </div>

        <div class="install-section">
          <h2>✨ Fonctionnalités</h2>
          <div class="feature-list">
            <div class="feature-item">
              <span>🔍</span>
              <div>Analyse et résolution automatisées des problèmes</div>
            </div>
            <div class="feature-item">
              <span>🔧</span>
              <div>Génération et modification intelligentes de code</div>
            </div>
            <div class="feature-item">
              <span>📋</span>
              <div>Automatisation des demandes de fusion</div>
            </div>
            <div class="feature-item">
              <span>🤖</span>
              <div>Support de multiples modèles LLM</div>
            </div>
            <div class="feature-item">
              <span>🐳</span>
              <div>Déploiement conteneurisé</div>
            </div>
            <div class="feature-item">
              <span>🔄</span>
              <div>Gestion de version facile</div>
            </div>
          </div>
        </div>

        <div class="footer">
          <p>
            Créé avec ❤️ par Lundi Matin |
            <a
              href="https://gitlab.lundimatin.app/artificial-intelligence-ia/neia"
              target="_blank"
              >Voir sur GitLab</a
            >
          </p>
        </div>
      </div>
    </div>
  </body>
</html>
