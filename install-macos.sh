#!/usr/bin/env bash

# NEIA Installation Script for macOS
# This script sets up NEIA for local use with Docker Desktop on macOS

set -e

# Verify we're running on macOS
if [[ "$(uname -s)" != "Darwin" ]]; then
    echo "❌ This script is designed for macOS only."
    echo "For Linux, use: curl -fsSL https://gitlab.lundimatin.app/artificial-intelligence-ia/neia/-/raw/main/install.sh | bash"
    exit 1
fi

NEIA_CONFIG_DIR="$HOME/.neia"
NEIA_IMAGE_REGISTRY="gitlab.lundimatin.app:5050/artificial-intelligence-ia/neia"
NEIA_VERSION="${NEIA_VERSION:-latest}"
NEIA_IMAGE="$NEIA_IMAGE_REGISTRY:$NEIA_VERSION"
NEIA_WRAPPER_SCRIPT="$HOME/.local/bin/neia"

# macOS-specific paths and settings
MACOS_VERSION=$(sw_vers -productVersion)
ARCH=$(uname -m)

# Colors for output (macOS Terminal compatible)
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Emojis for better UX on macOS
print_info() {
    echo -e "ℹ️  ${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "✅ ${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "⚠️  ${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "❌ ${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "🚀 ${PURPLE}[STEP]${NC} $1"
}

# Show usage information
show_usage() {
    echo "🤖 NEIA Installation Script for macOS"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -v, --version VERSION    Install specific version (default: latest)"
    echo "  -h, --help              Show this help message"
    echo "  --auto-install          Automatically install dependencies via Homebrew"
    echo ""
    echo "Environment variables:"
    echo "  NEIA_VERSION            Version to install (default: latest)"
    echo ""
    echo "Examples:"
    echo "  $0                      # Install latest version"
    echo "  $0 -v v1.0.0           # Install version v1.0.0"
    echo "  $0 --auto-install       # Auto-install Docker Desktop if missing"
    echo ""
    echo "Requirements:"
    echo "  - macOS 10.15+ (Catalina or later)"
    echo "  - Docker Desktop for Mac"
    echo "  - Git with SSH keys configured"
    echo ""
    echo "System Info:"
    echo "  - macOS Version: $MACOS_VERSION"
    echo "  - Architecture: $ARCH"
}

# Parse command line arguments
AUTO_INSTALL=false
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--version)
                NEIA_VERSION="$2"
                NEIA_IMAGE="$NEIA_IMAGE_REGISTRY:$NEIA_VERSION"
                shift 2
                ;;
            --auto-install)
                AUTO_INSTALL=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Check macOS version compatibility
check_macos_version() {
    print_step "Checking macOS compatibility..."
    
    local major_version=$(echo $MACOS_VERSION | cut -d. -f1)
    local minor_version=$(echo $MACOS_VERSION | cut -d. -f2)
    
    if [[ $major_version -lt 10 ]] || [[ $major_version -eq 10 && $minor_version -lt 15 ]]; then
        print_error "macOS 10.15 (Catalina) or later is required. You have $MACOS_VERSION"
        exit 1
    fi
    
    print_success "macOS $MACOS_VERSION is compatible"
}

# Check and install Homebrew if needed
check_homebrew() {
    print_step "Checking Homebrew..."
    
    if ! command -v brew &> /dev/null; then
        if [[ $AUTO_INSTALL == true ]]; then
            print_info "Installing Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            
            # Add Homebrew to PATH for Apple Silicon Macs
            if [[ $ARCH == "arm64" ]]; then
                echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
                eval "$(/opt/homebrew/bin/brew shellenv)"
            fi
        else
            print_warning "Homebrew not found. Install it for easier dependency management:"
            print_info "Visit: https://brew.sh or run with --auto-install"
        fi
    else
        print_success "Homebrew found: $(brew --version | head -1)"
    fi
}

# Check and install Docker Desktop
check_docker() {
    print_step "Checking Docker Desktop..."
    
    if ! command -v docker &> /dev/null; then
        if [[ $AUTO_INSTALL == true ]] && command -v brew &> /dev/null; then
            print_info "Installing Docker Desktop via Homebrew..."
            brew install --cask docker
            print_info "Please start Docker Desktop from Applications and return to continue"
            open -a Docker
            read -p "Press Enter after Docker Desktop has started..."
        else
            print_error "Docker Desktop is not installed."
            print_info "Install options:"
            print_info "1. Download from: https://docs.docker.com/desktop/install/mac-install/"
            print_info "2. Install via Homebrew: brew install --cask docker"
            print_info "3. Run this script with --auto-install"
            exit 1
        fi
    fi
    
    # Wait for Docker to be ready
    local retries=30
    while ! docker info &> /dev/null && [[ $retries -gt 0 ]]; do
        print_info "Waiting for Docker Desktop to start... ($retries attempts left)"
        sleep 2
        ((retries--))
    done
    
    if ! docker info &> /dev/null; then
        print_error "Docker Desktop is not running."
        print_info "Please start Docker Desktop from Applications:"
        print_info "• Open Spotlight (Cmd+Space) and search 'Docker'"
        print_info "• Or run: open -a Docker"
        exit 1
    fi
    
    print_success "Docker Desktop detected: $(docker --version)"
}

# Check SSH setup (important for macOS keychain integration)
check_ssh() {
    print_step "Checking SSH configuration..."
    
    if [[ ! -d "$HOME/.ssh" ]]; then
        print_warning "SSH directory not found. You may need to set up SSH keys."
        print_info "Generate SSH keys: ssh-keygen -t ed25519 -C '<EMAIL>'"
        return
    fi
    
    # Check for SSH keys
    local has_keys=false
    for key_type in id_rsa id_ed25519 id_ecdsa; do
        if [[ -f "$HOME/.ssh/$key_type" ]]; then
            has_keys=true
            break
        fi
    done
    
    if [[ $has_keys == false ]]; then
        print_warning "No SSH keys found. You may need to generate them."
        print_info "Generate SSH keys: ssh-keygen -t ed25519 -C '<EMAIL>'"
    fi
    
    # Check SSH agent
    if [[ -z "$SSH_AUTH_SOCK" ]]; then
        print_info "Starting SSH agent..."
        eval "$(ssh-agent -s)"
        
        # Add keys to agent
        ssh-add --apple-use-keychain ~/.ssh/id_* 2>/dev/null || true
        
        # Add to shell profile for persistence
        local shell_rc="$HOME/.zshrc"
        if [[ "$SHELL" == */bash ]]; then
            shell_rc="$HOME/.bash_profile"
        fi
        
        if ! grep -q "ssh-agent" "$shell_rc" 2>/dev/null; then
            echo "" >> "$shell_rc"
            echo "# SSH Agent (added by NEIA installer)" >> "$shell_rc"
            echo 'if [[ -z "$SSH_AUTH_SOCK" ]]; then' >> "$shell_rc"
            echo '    eval "$(ssh-agent -s)"' >> "$shell_rc"
            echo '    ssh-add --apple-use-keychain ~/.ssh/id_* 2>/dev/null || true' >> "$shell_rc"
            echo 'fi' >> "$shell_rc"
        fi
    else
        print_success "SSH agent is running"
    fi
    
    # Check SSH config for macOS keychain integration
    if [[ ! -f "$HOME/.ssh/config" ]] || ! grep -q "UseKeychain yes" "$HOME/.ssh/config"; then
        print_warning "SSH config may not be optimized for macOS keychain integration"
        print_info "For better SSH experience, consider adding to ~/.ssh/config:"
        echo -e "${YELLOW}# macOS keychain integration"
        echo -e "Host *"
        echo -e "    UseKeychain yes"
        echo -e "    AddKeysToAgent yes"
        echo -e "    IdentityFile ~/.ssh/id_ed25519"
        echo -e "    IdentityFile ~/.ssh/id_rsa${NC}"
        print_info "You can edit your SSH config with: nano ~/.ssh/config"
    else
        print_success "SSH config appears to have macOS keychain integration"
    fi
    
    print_success "SSH configuration complete"
}

# Create NEIA configuration directory structure
setup_config_dir() {
    print_step "Setting up NEIA configuration..."
    
    mkdir -p "$NEIA_CONFIG_DIR"
    mkdir -p "$NEIA_CONFIG_DIR/projects"
    mkdir -p "$NEIA_CONFIG_DIR/cache"
    
    # Create default configuration file if it doesn't exist
    if [[ ! -f "$NEIA_CONFIG_DIR/config.env" ]]; then
        print_info "Creating default configuration file..."
        cat > "$NEIA_CONFIG_DIR/config.env" << 'EOF'
# NEIA Configuration File for macOS
# Edit with your credentials: neia config

# GitLab Configuration
GITLAB_URL=https://********************.com
GITLAB_TOKEN=your-personal-access-token
GITLAB_PROJECT_ID=your-project-id

# Tuleap Configuration (optional)
TULEAP_API_URL=https://your-tuleap-instance.com
TULEAP_USER=your-tuleap-user
TULEAP_PASSWORD=your-tuleap-password

# LLM API Keys (choose one)
OPENAI_API_KEY=your-openai-api-key

# Default LLM Model (see https://aider.chat/docs/llms.html)
LLM_MODEL=gpt-4o-mini
EOF
        print_warning "Please edit $NEIA_CONFIG_DIR/config.env with your credentials"
        print_info "You can edit it later with: neia config"
    else
        print_success "Configuration file already exists"
    fi
    
    # Store the version information
    echo "NEIA_VERSION=$NEIA_VERSION" > "$NEIA_CONFIG_DIR/.version"
    echo "NEIA_IMAGE=$NEIA_IMAGE" >> "$NEIA_CONFIG_DIR/.version"
    echo "NEIA_ARCH=$ARCH" >> "$NEIA_CONFIG_DIR/.version"
    echo "NEIA_MACOS_VERSION=$MACOS_VERSION" >> "$NEIA_CONFIG_DIR/.version"
    
    print_success "Configuration directory set up at $NEIA_CONFIG_DIR"
}

# Pull the NEIA Docker image
pull_image() {
    print_step "Pulling NEIA Docker image..."
    
    print_info "Downloading $NEIA_IMAGE..."
    if docker pull "$NEIA_IMAGE"; then
        print_success "NEIA Docker image downloaded successfully"
    else
        print_error "Failed to pull NEIA Docker image: $NEIA_IMAGE"
        print_info "Available versions: https://gitlab.lundimatin.app/artificial-intelligence-ia/neia/container_registry"
        exit 1
    fi
}

# Create the neia wrapper script (macOS optimized)
create_wrapper() {
    print_step "Creating NEIA wrapper script..."
    
    # Ensure .local/bin exists
    mkdir -p "$(dirname "$NEIA_WRAPPER_SCRIPT")"
    
    cat > "$NEIA_WRAPPER_SCRIPT" << 'EOF'
#!/bin/bash

# NEIA Docker Wrapper Script for macOS
# Optimized for Docker Desktop and macOS conventions

NEIA_CONFIG_DIR="$HOME/.neia"
NEIA_IMAGE_REGISTRY="gitlab.lundimatin.app:5050/artificial-intelligence-ia/neia"
CONTAINER_NAME="neia-instance"

# Load version information
if [[ -f "$NEIA_CONFIG_DIR/.version" ]]; then
    source "$NEIA_CONFIG_DIR/.version"
else
    NEIA_VERSION="latest"
    NEIA_IMAGE="$NEIA_IMAGE_REGISTRY:latest"
fi

# Function to run NEIA command
run_neia() {
    # Stop any existing container
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
    
    # macOS-specific SSH agent setup - handle Docker Desktop SSH socket mounting issues
    SSH_ARGS=""
    if [[ -n "$SSH_AUTH_SOCK" ]]; then
        # On macOS, SSH agent socket paths can be problematic for Docker Desktop
        # Skip SSH agent mounting if the path looks like a launchd socket (common issue)
        if [[ "$SSH_AUTH_SOCK" == *"/com.apple.launchd."* ]]; then
            echo "⚠️  Detected macOS launchd SSH socket - skipping agent forwarding"
            echo "💡 NEIA will use your SSH keys from ~/.ssh/ instead"
            echo "💡 For SSH agent forwarding, run: eval \"\$(ssh-agent -s)\" && ssh-add"
        else
            SSH_ARGS="-v $SSH_AUTH_SOCK:/ssh-agent -e SSH_AUTH_SOCK=/ssh-agent"
            echo "✅ Using SSH agent forwarding"
        fi
    else
        echo "⚠️  SSH_AUTH_SOCK not set. Consider running: eval \"\$(ssh-agent -s)\" && ssh-add"
        echo "💡 NEIA will use your SSH keys from ~/.ssh/ instead"
    fi
    
    # Mount SSH directory (Docker Desktop handles this well on macOS)
    SSH_MOUNT_ARGS=""
    if [[ -d "$HOME/.ssh" ]]; then
        SSH_MOUNT_ARGS="-v $HOME/.ssh:/home/<USER>/.ssh:ro"
    fi
    
    # Additional environment variables for macOS
    SSH_ENV_ARGS="-e HOME=/home/<USER>"

    # Run the container with macOS-optimized settings
    args=(
        --rm -it
        --name "$CONTAINER_NAME"
        --env-file "$NEIA_CONFIG_DIR/config.env"
        $SSH_ARGS
        $SSH_MOUNT_ARGS
        $SSH_ENV_ARGS
        -v "$NEIA_CONFIG_DIR":/home/<USER>/.neia
        -v "$NEIA_CONFIG_DIR/projects":/app/projects
        -w /app
        "$NEIA_IMAGE"
        "$@"
    )
    docker run "${args[@]}"
}

# Function to update NEIA
update_neia() {
    local target_version="${1:-latest}"
    local new_image="$NEIA_IMAGE_REGISTRY:$target_version"
    
    echo "🔄 Updating NEIA to version: $target_version"
    if docker pull "$new_image"; then
        # Update version file
        echo "NEIA_VERSION=$target_version" > "$NEIA_CONFIG_DIR/.version"
        echo "NEIA_IMAGE=$new_image" >> "$NEIA_CONFIG_DIR/.version"
        echo "NEIA_ARCH=$(uname -m)" >> "$NEIA_CONFIG_DIR/.version"
        echo "NEIA_MACOS_VERSION=$(sw_vers -productVersion)" >> "$NEIA_CONFIG_DIR/.version"
        echo "✅ NEIA updated successfully to version: $target_version"
    else
        echo "❌ Failed to pull NEIA version: $target_version"
        echo "Available versions: https://gitlab.lundimatin.app/artificial-intelligence-ia/neia/container_registry"
        exit 1
    fi
}

# Handle special commands
case "$1" in
    "update")
        shift
        update_neia "$@"
        ;;
    "version")
        echo "🤖 NEIA version: $NEIA_VERSION"
        echo "📦 Docker image: $NEIA_IMAGE"
        echo "💻 Architecture: ${NEIA_ARCH:-$(uname -m)}"
        echo "🍎 macOS version: ${NEIA_MACOS_VERSION:-$(sw_vers -productVersion)}"
        if command -v docker &> /dev/null; then
            if docker image inspect "$NEIA_IMAGE" &> /dev/null; then
                echo "✅ Image status: Available locally"
            else
                echo "⚠️  Image status: Not available locally (run 'neia update' to pull)"
            fi
        fi
        ;;
    "config")
        echo "📝 Opening NEIA configuration file..."
        if command -v code &> /dev/null; then
            code "$NEIA_CONFIG_DIR/config.env"
        elif command -v nano &> /dev/null; then
            nano "$NEIA_CONFIG_DIR/config.env"
        else
            open -t "$NEIA_CONFIG_DIR/config.env"
        fi
        ;;
    "logs")
        docker logs "$CONTAINER_NAME" 2>/dev/null || echo "No running NEIA container found"
        ;;
    "status")
        echo "🤖 NEIA version: $NEIA_VERSION"
        echo "🐳 Docker Desktop: $(docker --version 2>/dev/null || echo 'Not running')"
        if docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}" | grep -q "$CONTAINER_NAME"; then
            echo "✅ NEIA container is running"
            docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        else
            echo "💤 NEIA container is not running"
        fi
        ;;
    "clean-cache")
        echo "🧹 Cleaning NEIA cache..."
        if [[ -d "$NEIA_CONFIG_DIR/projects" ]]; then
            echo "🗑️  Removing projects directory: $NEIA_CONFIG_DIR/projects"
            rm -rf "$NEIA_CONFIG_DIR/projects"
            mkdir -p "$NEIA_CONFIG_DIR/projects"
            echo "✅ Cache cleaned successfully!"
        else
            echo "⚠️  Projects directory not found: $NEIA_CONFIG_DIR/projects"
        fi
        ;;
    "clean-images")
        echo "🧹 Cleaning NEIA Docker images..."
        
        # Stop any running container first
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        
        # Remove all NEIA Docker images
        NEIA_IMAGES=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "^$NEIA_IMAGE_REGISTRY:" || true)
        if [[ -n "$NEIA_IMAGES" ]]; then
            echo "Found NEIA images:"
            echo "$NEIA_IMAGES"
            echo "$NEIA_IMAGES" | xargs -r docker rmi 2>/dev/null || echo "Warning: Some NEIA images could not be removed"
            echo "NEIA Docker images cleaned successfully!"
        else
            echo "No NEIA Docker images found locally"
        fi
        ;;
    "uninstall")
        echo "NEIA Uninstaller"
        echo "===================="
        echo ""
        
        # Stop and remove any running container
        echo "Stopping and removing NEIA container..."
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        
        # Remove all NEIA Docker images
        echo "🗑️  Removing all NEIA Docker images..."
        NEIA_IMAGES=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "^$NEIA_IMAGE_REGISTRY:" || true)
        if [[ -n "$NEIA_IMAGES" ]]; then
            echo "Found NEIA images:"
            echo "$NEIA_IMAGES"
            echo "$NEIA_IMAGES" | xargs -r docker rmi 2>/dev/null || echo "Warning: Some NEIA images could not be removed"
            echo "NEIA Docker images removed"
        else
            echo "No NEIA Docker images found locally"
        fi
        
        # Remove projects directory
        if [[ -d "$NEIA_CONFIG_DIR/projects" ]]; then
            echo "🗑️  Removing projects directory: $NEIA_CONFIG_DIR/projects"
            rm -rf "$NEIA_CONFIG_DIR/projects"
        fi
        
        # Ask about configuration removal
        echo ""
        echo "Do you want to remove the NEIA configuration directory ($NEIA_CONFIG_DIR)?"
        echo "   This will delete your config.env file."
        read -p "Remove configuration? [y/N]: " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "Removing configuration directory: $NEIA_CONFIG_DIR"
            rm -rf "$NEIA_CONFIG_DIR"
        else
            echo "Keeping configuration directory: $NEIA_CONFIG_DIR"
        fi
        
        # Remove wrapper script (this script itself)
        echo "Removing NEIA wrapper script: $0"
        rm -f "$0"
        
        echo ""
        echo "🎉 NEIA has been uninstalled successfully!"
        echo "💡 Note: You may want to remove the PATH entry from your shell configuration file."
        ;;
    "open-config")
        echo "📂 Opening NEIA configuration directory..."
        open "$NEIA_CONFIG_DIR"
        ;;
    "help-wrapper"|"--help-wrapper")
        echo "🤖 NEIA - AI-powered GitLab assistant for macOS"
        echo ""
        echo "Usage: neia <command> [options]"
        echo ""
        echo "Management commands:"
        echo "  update [version]              Update NEIA to latest or specific version"
        echo "  version                       Show current NEIA version and system info"
        echo "  config                        Edit NEIA configuration file"
        echo "  status                        Show NEIA container and Docker status"
        echo "  logs                          Show NEIA container logs"
        echo "  clean-cache                   Clean NEIA projects cache"
        echo "  clean-images                  Remove all NEIA Docker images"
        echo "  open-config                   Open configuration directory in Finder"
        echo "  uninstall                     Uninstall NEIA completely"
        echo ""
        echo "Examples:"
        echo "  neia update                   # Update to latest version"
        echo "  neia update v1.0.0            # Update to specific version"
        echo "  neia uninstall                # Remove NEIA completely"
        echo ""
        echo "Current version: $NEIA_VERSION"
        echo "Configuration file: $NEIA_CONFIG_DIR/config.env"
        ;;
    *)
        run_neia "$@"
        ;;
esac
EOF
    
    chmod +x "$NEIA_WRAPPER_SCRIPT"
    print_success "NEIA wrapper script created at $NEIA_WRAPPER_SCRIPT"
}

# Add to PATH (macOS shell detection)
setup_path() {
    print_step "Setting up PATH..."
    
    local shell_rc_file=""
    local bin_dir="$(dirname "$NEIA_WRAPPER_SCRIPT")"
    
    # Detect shell (macOS default is zsh since Catalina)
    case "$SHELL" in
        */zsh)
            shell_rc_file="$HOME/.zshrc"
            ;;
        */bash)
            shell_rc_file="$HOME/.bash_profile"
            [[ ! -f "$shell_rc_file" ]] && shell_rc_file="$HOME/.bashrc"
            ;;
        */fish)
            shell_rc_file="$HOME/.config/fish/config.fish"
            ;;
        *)
            shell_rc_file="$HOME/.profile"
            ;;
    esac
    
    # Check if the bin directory is already in PATH
    if [[ ":$PATH:" != *":$bin_dir:"* ]]; then
        print_info "Adding $bin_dir to PATH in $shell_rc_file"
        
        if [[ -f "$shell_rc_file" ]]; then
            # Check if the export line already exists
            if ! grep -q "export PATH.*$bin_dir" "$shell_rc_file"; then
                echo "" >> "$shell_rc_file"
                echo "# Added by NEIA installer" >> "$shell_rc_file"
                echo "export PATH=\"$bin_dir:\$PATH\"" >> "$shell_rc_file"
                print_success "Added $bin_dir to PATH"
            else
                print_info "$bin_dir is already in PATH"
            fi
        else
            print_warning "Could not find shell configuration file: $shell_rc_file"
            print_info "Please manually add $bin_dir to your PATH"
        fi
    else
        print_success "$bin_dir is already in PATH"
    fi
}

# Main installation process
main() {
    echo ""
    echo "🤖 =================================="
    echo "🍎 NEIA Installation for macOS"
    echo "🤖 =================================="
    echo ""
    
    # Parse command line arguments
    parse_args "$@"
    
    print_info "Installing NEIA version: $NEIA_VERSION"
    print_info "System: macOS $MACOS_VERSION ($ARCH)"
    echo ""
    
    check_macos_version
    check_homebrew
    check_docker
    check_ssh
    setup_config_dir
    pull_image
    create_wrapper
    setup_path
    
    echo ""
    echo "🎉 =================================="
    print_success "NEIA installation completed!"
    echo "🎉 =================================="
    echo ""
    print_info "Installed version: $NEIA_VERSION"
    print_info "Next steps:"
    echo "1. 📝 Edit your configuration: neia config"
    echo "2. 🔄 Reload your shell: source ~/.zshrc (or restart Terminal)"
    echo "3. 🧪 Test NEIA: neia --help"
    echo "4. 📂 Open config folder: neia open-config"
    echo ""
    print_info "Example usage:"
    echo "  neia process-issue https://gitlab.com/owner/repo/-/issues/123"
    echo "  neia list-mrs"
    echo "  neia update        # Update to latest version"
    echo "  neia version       # Check current version"
    echo ""
    print_info "Happy coding! 🚀"
}

main "$@" 