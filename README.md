# NEIA

NEIA is an AI-powered GitLab assistant that helps automate repository changes based on issue descriptions.

## Quick Installation (Recommended)

For the easiest installation experience, use our one-liner installer. See https://docs.google.com/document/d/103wvdJBJ8NJ1fG-dmA-amLy6MKEQFOhkdH_tCMc7cnU/edit?tab=t.0#heading=h.eevq6brh2j8x

## Manual Installation

### Local Installation

```shell
# Clone the repository
git clone https://gitlab.lundimatin.app/artificial-intelligence-ia/neia.git
cd neia

# Install poetry if you haven't already
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install
```

### Docker Installation

You can also run NEIA using Docker manually:

```shell
# Clone the repository
git clone https://gitlab.lundimatin.app/artificial-intelligence-ia/neia.git
cd neia

# Build the Docker image
docker build -t neia .
```

## Configuration

### Quick Installation Configuration

#### Linux/macOS Configuration

After using the quick installer, your configuration is stored in `~/.neia/config.env`. Edit it with:

```shell
neia config
```

_Note: You can specify the editor to use with the `EDITOR` environment variable. Nano is used by default._

#### Windows Configuration

After using the PowerShell installer, your configuration is stored in `%USERPROFILE%\.neia\config.env`. Edit it with:

```powershell
neia config
```

_Note: You can specify the editor to use with the `EDITOR` environment variable. Notepad is used by default on Windows._

The configuration file contains:

- GitLab credentials (URL and token)
- Tuleap credentials (optional)
- LLM API keys (OpenAI or DeepSeek)
- Default LLM model settings

### Local Configuration

Copy the file `.env.example` to `.env` and set up your credentials.

Besides your credentials, you can also declare your level of known technologies using keys in form `TECHNOLOGY_LEVEL_[...]` (for example `TECHNOLOGY_LEVEL_PYTHON`). The responses of NEIA will then be adapted to your level.

### Docker Configuration

When running with Docker, you can pass environment variables to the container.

## Usage

### Quick Installation Usage

Once installed with the quick installer, you can use NEIA from anywhere:

```shell
# Process an issue
neia process-issue <issue-url>

# List merge requests
neia list-mrs

# Show merge request details
neia show-mr <mr-iid>

# Process a merge request
neia process-mr <mr-iid>

# Review a merge request (automated code review)
neia review-mr <mr-iid>

# Review a merge request (dry run mode)
neia review-mr <mr-iid> --dry-run

# Generate Google Docs token
neia generate-google-token
```

### Automated Code Review

NEIA provides automated code review capabilities for GitLab merge requests. The review feature analyzes code changes and provides:

- **Quality Assessment**: Scores code on multiple metrics (0-10 scale)
- **Inline Comments**: Specific feedback following [Conventional Comments](https://conventionalcomments.org/) format
- **Definition of Done**: Checks against common DoD criteria
- **Security & Performance**: Identifies potential issues
- **Recommendations**: Actionable suggestions for improvement

#### Review Commands

```shell
# Review a specific merge request
neia review 123

# Dry run (analyze without posting to GitLab)
neia review 123 --dry-run
```

#### Review Features

- ✅ **Automated Quality Scoring**: Overall score based on multiple metrics
- 📝 **Inline Comments**: Code-specific feedback with severity levels
- 🛡️ **Security Analysis**: Identifies potential vulnerabilities
- ⚡ **Performance Review**: Spots efficiency concerns
- 📋 **Definition of Done**: Validates against DoD criteria
- 🔄 **Conventional Comments**: Follows standardized comment format
- 🎯 **Smart Analysis**: Uses AI to understand code context
- 📊 **Comprehensive Reports**: Detailed analysis with recommendations

#### Review Output

The automated review generates:

1. **Summary Comment**: Overall assessment with global score
2. **Quality Metrics**: Detailed breakdown of code quality aspects
3. **Inline Comments**: File and line-specific feedback
4. **Recommendations**: Actionable next steps
5. **Definition of Done Status**: Compliance check

All review comments are prefixed with `[NEIA]` to identify them as automated feedback.

```shell
# Update NEIA to latest version
neia update

# Update to specific version
neia update v1.0.0

# Check current version
neia version

# Check status
neia status

# View logs
neia logs

```

The issue can be a GitLab issue, Tuleap artifact or Google Docs TODO.

Example:

```shell
neia process-issue https://gitlab.lundimatin.app/artificial-intelligence-ia/neia-playground-apps/-/issues/1
```

### Local Usage

You can process an issue by providing your GitLab project ID and issue URL:

```shell
poetry run neia process-issue <issue-url>
```

The issue can be a GitLab issue, Tuleap artifact or Google Docs TODO.

Example:

```shell
poetry run neia process-issue https://gitlab.lundimatin.app/artificial-intelligence-ia/neia-playground-apps/-/issues/1
```

In order to process a Google Docs file, a valid token file must be present in the root folder, in `token.json` file (a different file can be specified via option `--google-token-file`).

Before the first run, the token file must be generated using the script:

```shell
poetry run neia generate-google-token
```

A file `credentials.json` (or another file defined in `--credentials-file`) must be present in the root folder in order to generate the token. The token is saved into `token.json` file (or different file if defined via the option `--token-file`):

```shell
poetry run neia generate-google-token --credentials-file <credentials-file> --token-file <token-file>
```

You can obtain the list of all merge requests assigned to NEIA using this command:

```shell
poetry run neia list-mrs
```

By running this command, you can also interactively obtain more details about a merge request or process a selected merge request.

To export the list of merge requests, you can precise the format (`json` or `csv`) via parameter `--export`:

```shell
poetry run neia list-mrs --export <export-format>
```

To get more details about an existing merge request, you can also use the command (with ID from the list of MR, not IID):

```shell
poetry run neia show-mr <mr-id>
```

You can process a single merge request directly by running the command (with ID from the list of MR, not IID):

```shell
poetry run neia process-mr <mr-id>
```

### Docker Usage

You can run NEIA using Docker in two ways:

#### Using Docker directly

```shell
docker run -e GITLAB_URL="https://gitlab.lundimatin.app" \
  -e GITLAB_TOKEN="your-personal-access-token" \
  -e TULEAP_API_URL="https://tuleap.lundimatin.biz" \
  -e TULEAP_USER="tuleap-user" \
  -e TULEAP_PASSWORD="tuleap-password" \
  -v $(pwd)/projects:/app/projects \
  neia process-issue <issue-url> --project-id <project-id>
```

#### Using Docker Compose

```shell
# Set your environment variables
export GITLAB_URL="https://********************.com"
export GITLAB_TOKEN="your-personal-access-token"
export TULEAP_API_URL="https://tuleap.lundimatin.biz"
export TULEAP_USER="tuleap-user"
export TULEAP_PASSWORD="tuleap-password"
export NEIA_ARGS="--project-id <project-id>"

# Run with docker-compose
docker-compose up
```

NEIA will:

1. Analyze the issue description
2. Create a new branch
3. Apply necessary changes
4. Create a merge request with the changes

## Options

### LLM Models (`--llm-model`)

You can run NEIA with different models by specifying the `LLM_MODEL` parameter in the `.env` file. By default, NEIA uses the `gpt-4o-mini` model.

See https://aider.chat/docs/llms.html for a list of available models and https://aider.chat/docs/leaderboards/ for the leaderboard.

### Dry run (`--dry-run`)

You can run NEIA in dry-run mode to see what changes would be applied without actually applying them:

```shell
poetry run neia process-issue <issue-url> --dry-run
```

### Only show prompts (`--prompt-only`)

You can run NEIA in prompt-only mode to only show prompts without applying any changes and without calling the LLM API.\
It is convenient to test the prompts and the surrounding logic without costing any API calls.

```shell
poetry run neia process-issue <issue-url> --prompt-only
```

These three options are available for `process-issue` but also for `process-mr`.

## Code checks

You can run the code checks using the following command:

```shell
make lint
```

You can also run the code checks and fix the issues using the following command:

```shell
make lint-fix
```

## Tests

You can run the tests using the following command:

```shell
make test
```

## Releases

NEIA uses semantic versioning (x.y.z) for releases. You can create new releases using the provided Makefile.

### Prerequisites

- Ensure you're on the main branch with latest changes
- Have proper Git permissions to push tags to the repository
- Python 3 installed (for version calculation)

### Creating a Release

Use the Makefile to create different types of releases:

```shell
# Create a patch release (e.g., 0.1.0 → 0.1.1)
make release-patch

# Create a minor release (e.g., 0.1.1 → 0.2.0)
make release-minor

# Create a major release (e.g., 0.2.0 → 1.0.0)
make release-major

# Show available commands and current version
make help
```

### What happens during a release

The release process automatically:

1. **Checks out main branch** and pulls latest changes
2. **Calculates new version** based on the current version in `pyproject.toml`
3. **Updates `pyproject.toml`** with the new version
4. **Commits the version change** with a descriptive message
5. **Creates a Git tag** in the format `vx.y.z` with a version message
6. **Pushes changes and tags** to the origin repository

### Example

```shell
$ make release-patch
```

### Post-Release Steps

After creating a release:

1. **Create a GitLab release** from the new tag with release notes
2. **Update changelog** if maintained separately
3. **Verify the release** appears correctly in the project

### Troubleshooting

- If you get permission errors, ensure you have push access to the repository
- If version parsing fails, check that `pyproject.toml` follows the expected format: `version = "x.y.z"`
- Use `make help` to see current version and available commands

## License

This project is private and the property of Lundi Matin.

## Project status

This project is in its early prototype stage and is actively evolving. As such, it is not yet stable and may undergo significant changes. Users should be aware that features and functionality might change frequently as development progresses.
