#!/usr/bin/env bash

# NEIA Installation Script
# This script sets up NEIA for local use with Docker

set -e

NEIA_CONFIG_DIR="$HOME/.neia"
NEIA_IMAGE_REGISTRY="gitlab.lundimatin.app:5050/artificial-intelligence-ia/neia"
NEIA_VERSION="${NEIA_VERSION:-latest}"
NEIA_IMAGE="$NEIA_IMAGE_REGISTRY:$NEIA_VERSION"
NEIA_WRAPPER_SCRIPT="$HOME/.local/bin/neia"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage information
show_usage() {
    echo "NEIA Installation Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -v, --version VERSION    Install specific version (default: latest)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  NEIA_VERSION            Version to install (default: latest)"
    echo ""
    echo "Examples:"
    echo "  $0                      # Install latest version"
    echo "  $0 -v v1.0.0           # Install version v1.0.0"
    echo "  NEIA_VERSION=v1.0.0 $0  # Install version v1.0.0"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--version)
                NEIA_VERSION="$2"
                NEIA_IMAGE="$NEIA_IMAGE_REGISTRY:$NEIA_VERSION"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Check if Docker is installed and running
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Create NEIA configuration directory structure
setup_config_dir() {
    print_info "Setting up NEIA configuration directory..."
    
    mkdir -p "$NEIA_CONFIG_DIR"
    mkdir -p "$NEIA_CONFIG_DIR/projects"
    
    # Create default configuration file if it doesn't exist
    if [ ! -f "$NEIA_CONFIG_DIR/config.env" ]; then
        print_info "Creating default configuration file..."
        cat > "$NEIA_CONFIG_DIR/config.env" << 'EOF'
# NEIA Configuration File
# Copy this file and update with your credentials

# GitLab Configuration
GITLAB_URL=https://********************.com
GITLAB_TOKEN=your-personal-access-token
GITLAB_PROJECT_ID=your-project-id

# Tuleap Configuration (optional)
TULEAP_API_URL=https://your-tuleap-instance.com
TULEAP_USER=your-tuleap-user
TULEAP_PASSWORD=your-tuleap-password

# Google Docs Configuration (optional)
GOOGLE_TOKEN_FILE=token.json

# LLM API Keys (choose one)
OPENAI_API_KEY=your-openai-api-key
# Default LLM Model (see https://aider.chat/docs/llms.html)
LLM_MODEL=gpt-4o-mini
EOF
        print_warning "Please edit $NEIA_CONFIG_DIR/config.env with your credentials before using NEIA."
    else
        print_info "Configuration file already exists at $NEIA_CONFIG_DIR/config.env"
    fi
    
    # Store the version information
    echo "NEIA_VERSION=$NEIA_VERSION" > "$NEIA_CONFIG_DIR/.version"
    echo "NEIA_IMAGE=$NEIA_IMAGE" >> "$NEIA_CONFIG_DIR/.version"
    
    print_success "Configuration directory set up at $NEIA_CONFIG_DIR"
}

# Pull the latest NEIA Docker image
pull_image() {
    print_info "Pulling NEIA Docker image: $NEIA_IMAGE"
    if docker pull "$NEIA_IMAGE"; then
        print_success "NEIA Docker image pulled successfully"
    else
        print_error "Failed to pull NEIA Docker image: $NEIA_IMAGE"
        print_info "Available versions can be found at: https://gitlab.lundimatin.app/artificial-intelligence-ia/neia/container_registry"
        exit 1
    fi
}

# Create the neia wrapper script
create_wrapper() {
    print_info "Creating NEIA wrapper script..."
    
    # Ensure .local/bin exists
    mkdir -p "$(dirname "$NEIA_WRAPPER_SCRIPT")"
    
    cat > "$NEIA_WRAPPER_SCRIPT" << 'EOF'
#!/bin/bash

# NEIA Docker Wrapper Script
# This script provides an easy interface to run NEIA commands

NEIA_CONFIG_DIR="$HOME/.neia"
NEIA_IMAGE_REGISTRY="gitlab.lundimatin.app:5050/artificial-intelligence-ia/neia"
CONTAINER_NAME="neia-instance"

# Load version information
if [ -f "$NEIA_CONFIG_DIR/.version" ]; then
    source "$NEIA_CONFIG_DIR/.version"
else
    NEIA_VERSION="latest"
    NEIA_IMAGE="$NEIA_IMAGE_REGISTRY:latest"
fi

# Function to run NEIA command
run_neia() {
    # Stop any existing container
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
    
    # SSH agent forwarding and host SSH keys
    SSH_ARGS=""
    if [ -n "$SSH_AUTH_SOCK" ]; then
        SSH_ARGS="-v $SSH_AUTH_SOCK:/ssh-agent -e SSH_AUTH_SOCK=/ssh-agent"
    else
        echo "Warning: SSH_AUTH_SOCK not set. SSH agent forwarding will not work."
        echo "Make sure your SSH agent is running: ssh-add -l"
    fi
    
    # Mount host SSH directory for known_hosts and keys (if it exists)
    SSH_MOUNT_ARGS=""
    if [ -d "$HOME/.ssh" ]; then
        SSH_MOUNT_ARGS="-v $HOME/.ssh:/home/<USER>/.ssh:ro"
    fi
    
    # Additional environment variables for proper SSH setup
    SSH_ENV_ARGS="-e HOME=/home/<USER>"

    # Run the container with all configurations
    args=(
        --rm -it
        --name "$CONTAINER_NAME"
        --env-file "$NEIA_CONFIG_DIR/config.env"
        $SSH_ARGS
        $SSH_MOUNT_ARGS
        $SSH_ENV_ARGS
        -v "$NEIA_CONFIG_DIR":/home/<USER>/.neia
        -v "$NEIA_CONFIG_DIR/projects":/app/projects
        -w /app
        "$NEIA_IMAGE"
        "$@"
    )
    docker run "${args[@]}"
}

# Function to update NEIA
update_neia() {
    local target_version="${1:-latest}"
    local new_image="$NEIA_IMAGE_REGISTRY:$target_version"
    
    echo "Updating NEIA to version: $target_version"
    if docker pull "$new_image"; then
        # Update version file
        echo "NEIA_VERSION=$target_version" > "$NEIA_CONFIG_DIR/.version"
        echo "NEIA_IMAGE=$new_image" >> "$NEIA_CONFIG_DIR/.version"
        echo "NEIA updated successfully to version: $target_version"
    else
        echo "Failed to pull NEIA version: $target_version"
        echo "Available versions can be found at: https://gitlab.lundimatin.app/artificial-intelligence-ia/neia/container_registry"
        exit 1
    fi
}

# Handle special commands
case "$1" in
    "update")
        shift
        update_neia "$@"
        ;;
    "version")
        echo "NEIA version: $NEIA_VERSION"
        echo "Docker image: $NEIA_IMAGE"
        if command -v docker &> /dev/null; then
            if docker image inspect "$NEIA_IMAGE" &> /dev/null; then
                echo "Image status: Available locally"
            else
                echo "Image status: Not available locally (run 'neia update' to pull)"
            fi
        fi
        ;;
    "config")
        echo "Opening NEIA configuration file..."
        ${EDITOR:-nano} "$NEIA_CONFIG_DIR/config.env"
        ;;
    "logs")
        docker logs "$CONTAINER_NAME" 2>/dev/null || echo "No running NEIA container found"
        ;;
    "status")
        echo "NEIA version: $NEIA_VERSION"
        if docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}" | grep -q "$CONTAINER_NAME"; then
            echo "NEIA container is running"
            docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        else
            echo "NEIA container is not running"
        fi
        ;;
    "clean-cache")
        echo "Cleaning NEIA cache..."
        if [ -d "$NEIA_CONFIG_DIR/projects" ]; then
            echo "Removing projects directory: $NEIA_CONFIG_DIR/projects"
            rm -rf "$NEIA_CONFIG_DIR/projects"
            mkdir -p "$NEIA_CONFIG_DIR/projects"
            echo "Cache cleaned successfully!"
        else
            echo "Projects directory not found: $NEIA_CONFIG_DIR/projects"
        fi
        ;;
    "clean-images")
        echo "Cleaning NEIA Docker images..."
        
        # Stop any running container first
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        
        # Remove all NEIA Docker images
        NEIA_IMAGES=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "^$NEIA_IMAGE_REGISTRY:" || true)
        if [ -n "$NEIA_IMAGES" ]; then
            echo "Found NEIA images:"
            echo "$NEIA_IMAGES"
            echo "$NEIA_IMAGES" | xargs -r docker rmi 2>/dev/null || echo "Warning: Some NEIA images could not be removed"
            echo "NEIA Docker images cleaned successfully!"
        else
            echo "No NEIA Docker images found locally"
        fi
        ;;
    "uninstall")
        echo "NEIA Uninstaller"
        echo "================"
        echo ""
        
        # Stop and remove any running container
        echo "Stopping and removing NEIA container..."
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        
        # Remove all NEIA Docker images
        echo "Removing all NEIA Docker images..."
        NEIA_IMAGES=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "^$NEIA_IMAGE_REGISTRY:" || true)
        if [ -n "$NEIA_IMAGES" ]; then
            echo "Found NEIA images:"
            echo "$NEIA_IMAGES"
            echo "$NEIA_IMAGES" | xargs -r docker rmi 2>/dev/null || echo "Warning: Some NEIA images could not be removed"
            echo "NEIA Docker images removed"
        else
            echo "No NEIA Docker images found locally"
        fi
        
        # Remove projects directory
        if [ -d "$NEIA_CONFIG_DIR/projects" ]; then
            echo "Removing projects directory: $NEIA_CONFIG_DIR/projects"
            rm -rf "$NEIA_CONFIG_DIR/projects"
        fi
        
        # Ask about configuration removal
        echo ""
        echo "Do you want to remove the NEIA configuration directory ($NEIA_CONFIG_DIR)?"
        echo "This will delete your config.env file."
        read -p "Remove configuration? [y/N]: " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "Removing configuration directory: $NEIA_CONFIG_DIR"
            rm -rf "$NEIA_CONFIG_DIR"
        else
            echo "Keeping configuration directory: $NEIA_CONFIG_DIR"
        fi
        
        # Remove wrapper script (this script itself)
        echo "Removing NEIA wrapper script: $0"
        rm -f "$0"
        
        echo ""
        echo "NEIA has been uninstalled successfully!"
        echo "Note: You may want to remove the PATH entry from your shell configuration file."
        ;;
    "help-wrapper"|"--help-wrapper")
        echo "NEIA - AI-powered GitLab assistant"
        echo ""
        echo "Usage: neia <command> [options]"
        echo ""
        echo "Management commands:"
        echo "  update [version]              Update NEIA to latest or specific version"
        echo "  version                       Show current NEIA version"
        echo "  config                        Edit NEIA configuration"
        echo "  status                        Show NEIA container status"
        echo "  logs                          Show NEIA container logs"
        echo "  clean-cache                   Clean NEIA projects cache"
        echo "  clean-images                  Remove all NEIA Docker images"
        echo "  uninstall                     Uninstall NEIA completely"
        echo ""
        echo "Examples:"
        echo "  neia update                   # Update to latest version"
        echo "  neia update v1.0.0            # Update to specific version"
        echo "  neia uninstall                # Remove NEIA completely"
        echo ""
        echo "Current version: $NEIA_VERSION"
        echo "Configuration file: $NEIA_CONFIG_DIR/config.env"
        ;;
    *)
        run_neia "$@"
        ;;
esac
EOF
    
    chmod +x "$NEIA_WRAPPER_SCRIPT"
    print_success "NEIA wrapper script created at $NEIA_WRAPPER_SCRIPT"
}

# Add to PATH if needed
setup_path() {
    local shell_rc_file=""
    local bin_dir="$(dirname "$NEIA_WRAPPER_SCRIPT")"
    
    # Detect shell and corresponding RC file
    case "$SHELL" in
        */bash)
            shell_rc_file="$HOME/.bashrc"
            ;;
        */zsh)
            shell_rc_file="$HOME/.zshrc"
            ;;
        */fish)
            shell_rc_file="$HOME/.config/fish/config.fish"
            ;;
        *)
            shell_rc_file="$HOME/.profile"
            ;;
    esac
    
    # Check if the bin directory is already in PATH
    if [[ ":$PATH:" != *":$bin_dir:"* ]]; then
        print_info "Adding $bin_dir to PATH in $shell_rc_file"
        
        if [ -f "$shell_rc_file" ]; then
            # Check if the export line already exists
            if ! grep -q "export PATH.*$bin_dir" "$shell_rc_file"; then
                echo "" >> "$shell_rc_file"
                echo "# Added by NEIA installer" >> "$shell_rc_file"
                echo "export PATH=\"$bin_dir:\$PATH\"" >> "$shell_rc_file"
                print_success "Added $bin_dir to PATH"
            else
                print_info "$bin_dir is already in PATH"
            fi
        else
            print_warning "Could not find shell configuration file: $shell_rc_file"
            print_info "Please manually add $bin_dir to your PATH"
        fi
    else
        print_info "$bin_dir is already in PATH"
    fi
}

# Main installation process
main() {
    # Parse command line arguments
    parse_args "$@"
    
    echo "===================="
    echo "NEIA Installation"
    echo "===================="
    echo ""
    print_info "Installing NEIA version: $NEIA_VERSION"
    echo ""
    
    check_docker
    setup_config_dir
    pull_image
    create_wrapper
    setup_path
    
    echo ""
    echo "===================="
    print_success "NEIA installation completed!"
    echo "===================="
    echo ""
    print_info "Installed version: $NEIA_VERSION"
    print_info "Next steps:"
    echo "1. Edit your configuration: neia config"
    echo "2. Reload your shell or run: source $shell_rc_file"
    echo "3. Test NEIA: neia --help"
    echo ""
    print_info "Example usage:"
    echo "  neia process-issue https://gitlab.com/owner/repo/-/issues/123"
    echo "  neia list-mrs"
    echo "  neia update        # Update to latest version"
    echo "  neia version       # Check current version"
    echo ""
}

main "$@" 