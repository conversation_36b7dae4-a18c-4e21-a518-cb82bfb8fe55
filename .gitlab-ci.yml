variables:
  DOCKER_IMAGE_NAME: $CI_REGISTRY_IMAGE
  DOCKER_DRIVER: overlay2

stages:
  - quality
  - publish
  - deploy

# Code quality checks job - runs on every push/MR
code-quality:
  stage: quality
  image: python:3.11-slim
  before_script:
    - apt-get update -qq && apt-get install -y -qq git curl build-essential
    - pip install poetry
    - poetry config virtualenvs.create false
    - poetry install --with dev
    - mypy . || true
    - mypy --install-types --non-interactive
  script:
    - make lint
  rules:
    - if: $CI_COMMIT_BRANCH
    - if: $CI_MERGE_REQUEST_IID

# Tests
tests:
  stage: quality
  image: python:3.11-slim
  before_script:
    - apt-get update -qq && apt-get install -y -qq git curl build-essential
    - pip install poetry
    - poetry config virtualenvs.create false
    - poetry install --with dev
  script:
    - make test
  rules:
    - if: $CI_COMMIT_BRANCH
    - if: $CI_MERGE_REQUEST_IID

# Common template for Docker build jobs
.docker_build_template: &docker_build_template
  stage: publish
  image: docker:28
  services:
    - docker:28-dind
  variables:
    # Disable TLS to avoid certificate issues
    DOCKER_TLS_CERTDIR: ""
    DOCKER_HOST: tcp://docker:2375
  before_script:
    - echo "Waiting for Docker daemon to start..."
    - sleep 10
    - docker info
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - echo "Setting up Docker Buildx for multi-architecture builds..."
    - docker buildx create --name multiarch --driver docker-container --use
    - docker buildx inspect --bootstrap
    - docker buildx ls

# Build and publish for main branch (latest tag)
publish-latest:
  <<: *docker_build_template
  script:
    - echo "Building multi-architecture Docker image for latest..."
    - docker buildx build --provenance=false --platform linux/amd64,linux/arm64 --build-arg GOOGLE_CLIENT_SECRET="$GOOGLE_CLIENT_SECRET" -t $DOCKER_IMAGE_NAME:latest --push .
    - echo "Successfully published multi-architecture $DOCKER_IMAGE_NAME:latest"
  only:
    - main

# Build and publish for version tags
publish-version:
  <<: *docker_build_template
  script:
    - echo "Building multi-architecture Docker image for version $CI_COMMIT_TAG..."
    - export MAJOR_MINOR_VERSION=$(echo $CI_COMMIT_TAG | sed 's/\.[^.]*$//')
    - echo "Also building for version '$MAJOR_MINOR_VERSION'"
    - docker buildx build --provenance=false --platform linux/amd64,linux/arm64 --build-arg GOOGLE_CLIENT_SECRET="$GOOGLE_CLIENT_SECRET" -t $DOCKER_IMAGE_NAME:$CI_COMMIT_TAG -t $DOCKER_IMAGE_NAME:$MAJOR_MINOR_VERSION --push .
    - echo "Successfully published multi-architecture $DOCKER_IMAGE_NAME:$MAJOR_MINOR_VERSION and $DOCKER_IMAGE_NAME:$CI_COMMIT_TAG"
  only:
    - tags

# Build and publish for feature branches (optional)
publish-branch:
  <<: *docker_build_template
  script:
    - export SAFE_BRANCH_NAME=$(echo $CI_COMMIT_REF_NAME | sed 's/[^a-zA-Z0-9]/-/g' | tr '[:upper:]' '[:lower:]')
    - echo "Building multi-architecture Docker image for branch $SAFE_BRANCH_NAME..."
    - docker buildx build --provenance=false --platform linux/amd64,linux/arm64 --build-arg GOOGLE_CLIENT_SECRET="$GOOGLE_CLIENT_SECRET" -t $DOCKER_IMAGE_NAME:$SAFE_BRANCH_NAME --push .
    - echo "Successfully published multi-architecture $DOCKER_IMAGE_NAME:$SAFE_BRANCH_NAME"
  only:
    refs:
      - branches
    variables:
      - $CI_COMMIT_MESSAGE =~ /\[build-image\]/
  except:
    - main

# GitLab Pages job - Deploy install scripts and landing page
pages:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache git
  script:
    - echo "Preparing GitLab Pages deployment..."
    - mkdir -p public
    - echo "Copying install scripts to public directory..."
    - cp install-linux.sh public/ || echo "install-linux.sh not found"
    - cp install-macos.sh public/ || echo "install-macos.sh not found"
    - cp install-windows.ps1 public/ || echo "install-windows.ps1 not found"
    - echo "Landing page already exists at public/index.html"
    - ls -la public/
    - echo "GitLab Pages deployment ready"
  artifacts:
    paths:
      - public
    expire_in: 1 week
  only:
    changes:
      - install-linux.sh
      - install-macos.sh
      - install-windows.ps1
      - public/index.html
    refs:
      - main
  when: on_success
