FROM python:3.12-slim

WORKDIR /app

# Install git and required system dependencies
RUN apt-get update && apt-get install -y \
  git \
  openssh-client \
  && rm -rf /var/lib/apt/lists/*

# Create neia user with home directory
RUN useradd -m -s /bin/bash neia

# Install Poetry
RUN pip install poetry

# Set Poetry environment variables globally
ENV POETRY_VENV_IN_PROJECT=false
ENV POETRY_CACHE_DIR=/tmp/poetry_cache
ENV POETRY_NO_INTERACTION=1
ENV POETRY_VIRTUALENVS_CREATE=false

# Copy dependency files and source code
COPY pyproject.toml poetry.lock* ./
COPY neia/ ./neia/

# Generate credentials.json at build time using CI environment variable
ARG GOOGLE_CLIENT_SECRET
RUN echo '{ \
  "installed": { \
  "client_id": "**********-koinageh8gba26k631i87esi8ujp22rm.apps.googleusercontent.com", \
  "project_id": "neia-459012", \
  "auth_uri": "https://accounts.google.com/o/oauth2/auth", \
  "token_uri": "https://oauth2.googleapis.com/token", \
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", \
  "client_secret": "'"$GOOGLE_CLIENT_SECRET"'", \
  "redirect_uris": ["http://localhost"] \
  } \
  }' > credentials.json

# Install dependencies and the application itself
RUN poetry install --no-interaction && rm -rf /tmp/poetry_cache

# Create SSH directory for neia user and fix permissions
RUN mkdir -p /home/<USER>/.ssh && chown -R neia:neia /home/<USER>/home/<USER>/.ssh

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Switch to neia user
USER neia

# Default entrypoint
ENTRYPOINT ["poetry", "run", "neia"]

# Default args (can be overridden at runtime)
CMD ["--help"]
