#!/bin/bash

# NEIA - Code Quality Check Script
# This script runs the same formatting and typing checks as the CI pipeline
# Usage: ./lint.sh [--fix|-f]
#   --fix, -f: Auto-fix formatting and linting issues where possible

set -e  # Exit immediately if a command exits with a non-zero status

# Parse command line arguments
FIX_MODE=false
for arg in "$@"; do
    case $arg in
        --fix|-f)
            FIX_MODE=true
            shift
            ;;
        --help|-h)
            echo "NEIA Code Quality Check Script"
            echo "This script runs the same formatting and typing checks as the CI pipeline"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --fix, -f     Auto-fix formatting and linting issues where possible"
            echo "  --help, -h    Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0            # Run checks only (CI mode)"
            echo "  $0 --fix      # Run checks and auto-fix issues"
            echo "  $0 --help     # Show this help message"
            echo ""
            echo "What this script checks:"
            echo "  1. Ruff linting (with auto-fix support)"
            echo "  2. Black code formatting (with auto-fix support)"
            echo "  3. isort import sorting (with auto-fix support)"
            echo "  4. MyPy type checking (manual fixes required)"
            echo ""
            echo "Individual tool commands:"
            echo "  poetry run ruff check .           # Linting (check only)"
            echo "  poetry run ruff check --fix .     # Linting (with auto-fix)"
            echo "  poetry run black --check .        # Formatting (check only)"
            echo "  poetry run black .                # Formatting (with auto-fix)"
            echo "  poetry run isort --check-only .   # Import sorting (check only)"
            echo "  poetry run isort .                # Import sorting (with auto-fix)"
            echo "  poetry run mypy neia/             # Type checking"
            echo ""
            echo "Makefile shortcuts:"
            echo "  make lint      # Same as $0"
            echo "  make lint-fix  # Same as $0 --fix"
            exit 0
            ;;
        *)
            echo "Unknown option: $arg"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

if [ "$FIX_MODE" = true ]; then
    echo -e "${BLUE}🔧 Running code quality checks with auto-fix...${NC}"
else
    echo -e "${BLUE}🔍 Running code quality checks...${NC}"
fi
echo ""

# Check if poetry is available
if ! command -v poetry &> /dev/null; then
    echo -e "${RED}❌ Poetry is not installed. Please install Poetry first.${NC}"
    echo "Visit: https://python-poetry.org/docs/#installation"
    exit 1
fi

# Install dependencies if needed
echo -e "${BLUE}📦 Installing dependencies...${NC}"
poetry install --with dev

echo ""
echo -e "${BLUE}1️⃣  Running Ruff linter...${NC}"
if [ "$FIX_MODE" = true ]; then
    poetry run ruff check --fix .
    echo -e "${GREEN}✅ Ruff auto-fix completed${NC}"
else
    poetry run ruff check . || {
        echo -e "${RED}❌ Ruff linting failed. Fix the issues above.${NC}"
        echo -e "${YELLOW}💡 Run '$0 --fix' to auto-fix some issues.${NC}"
        exit 1
    }
    echo -e "${GREEN}✅ Ruff linting passed${NC}"
fi

echo ""
echo -e "${BLUE}2️⃣  Running Black formatter...${NC}"
if [ "$FIX_MODE" = true ]; then
    poetry run black .
    echo -e "${GREEN}✅ Black formatting completed${NC}"
else
    poetry run black --check --diff . || {
        echo -e "${RED}❌ Black formatting check failed.${NC}"
        echo -e "${YELLOW}💡 Run '$0 --fix' to auto-fix formatting issues.${NC}"
        exit 1
    }
    echo -e "${GREEN}✅ Black formatting check passed${NC}"
fi

echo ""
echo -e "${BLUE}3️⃣  Running isort import sorting...${NC}"
if [ "$FIX_MODE" = true ]; then
    poetry run isort .
    echo -e "${GREEN}✅ isort import sorting completed${NC}"
else
    poetry run isort --check-only --diff . || {
        echo -e "${RED}❌ isort import sorting check failed.${NC}"
        echo -e "${YELLOW}💡 Run '$0 --fix' to auto-fix import sorting issues.${NC}"
        exit 1
    }
    echo -e "${GREEN}✅ isort import sorting check passed${NC}"
fi

echo ""
echo -e "${BLUE}4️⃣  Running MyPy type checking...${NC}"
poetry run mypy neia/ || {
    echo -e "${RED}❌ MyPy type checking failed. Fix the type issues above.${NC}"
    if [ "$FIX_MODE" = true ]; then
        echo -e "${YELLOW}💡 MyPy errors require manual fixes - type annotations cannot be auto-fixed.${NC}"
    fi
    exit 1
}
echo -e "${GREEN}✅ MyPy type checking passed${NC}"
